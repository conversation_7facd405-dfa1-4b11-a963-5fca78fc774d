import React from 'react';
import { Link } from 'react-router-dom';
import { ShoppingBag, Plus, Minus, Trash2, ArrowLeft } from 'lucide-react';
import { useCart } from '../hooks/CartContext';

const Cart: React.FC = () => {
  const { items, removeFromCart, updateQuantity, total, clearCart } = useCart();

  if (items.length === 0) {
    return (
      <div className='min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center'>
        <div className='text-center'>
          <ShoppingBag className='h-24 w-24 text-gray-400 mx-auto mb-4' />
          <h2 className='text-2xl font-bold text-gray-900 dark:text-white mb-2'>
            Your cart is empty
          </h2>
          <p className='text-gray-600 dark:text-gray-300 mb-8'>
            Add some beautiful flowers to get started!
          </p>
          <Link
            to='/catalog'
            className='inline-flex items-center px-6 py-3 bg-emerald-600 hover:bg-emerald-700 text-white font-semibold rounded-lg transition-colors duration-200'
          >
            <ArrowLeft className='h-5 w-5 mr-2' />
            Continue Shopping
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gray-50 dark:bg-gray-900'>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'>
        <div className='flex items-center justify-between mb-8'>
          <h1 className='text-3xl font-bold text-gray-900 dark:text-white'>
            Shopping Cart
          </h1>
          <button
            onClick={clearCart}
            className='text-red-600 hover:text-red-700 font-medium'
          >
            Clear Cart
          </button>
        </div>

        <div className='grid grid-cols-1 lg:grid-cols-3 gap-8'>
          {/* Cart Items */}
          <div className='lg:col-span-2 space-y-4'>
            {items.map((item) => (
              <div
                key={`${item.product.id}-${item.selectedColor}`}
                className='bg-white dark:bg-gray-800 rounded-lg shadow-md p-6'
              >
                <div className='flex items-center space-x-4'>
                  <img
                    src={item.product.image}
                    alt={item.product.name}
                    className='w-20 h-20 object-cover rounded-lg'
                  />
                  <div className='flex-1'>
                    <h3 className='text-lg font-semibold text-gray-900 dark:text-white'>
                      {item.product.name}
                    </h3>
                    <p className='text-gray-600 dark:text-gray-300 text-sm'>
                      {item.product.description}
                    </p>
                    {item.selectedColor && (
                      <p className='text-sm text-gray-500 dark:text-gray-400'>
                        Color: {item.selectedColor}
                      </p>
                    )}
                    <p className='text-lg font-semibold text-emerald-600 dark:text-emerald-400'>
                      ${item.product.price.toFixed(2)}
                    </p>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <button
                      onClick={() =>
                        updateQuantity(item.product.id, item.quantity - 1)
                      }
                      className='p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded'
                    >
                      <Minus className='h-4 w-4' />
                    </button>
                    <span className='w-8 text-center font-semibold text-gray-900 dark:text-white'>
                      {item.quantity}
                    </span>
                    <button
                      onClick={() =>
                        updateQuantity(item.product.id, item.quantity + 1)
                      }
                      className='p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded'
                    >
                      <Plus className='h-4 w-4' />
                    </button>
                  </div>
                  <button
                    onClick={() => removeFromCart(item.product.id)}
                    className='p-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900 rounded-lg'
                  >
                    <Trash2 className='h-5 w-5' />
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* Order Summary */}
          <div className='bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 h-fit'>
            <h2 className='text-xl font-bold text-gray-900 dark:text-white mb-4'>
              Order Summary
            </h2>

            <div className='space-y-2 mb-4'>
              <div className='flex justify-between'>
                <span className='text-gray-600 dark:text-gray-300'>
                  Subtotal
                </span>
                <span className='font-semibold text-gray-900 dark:text-white'>
                  ${total.toFixed(2)}
                </span>
              </div>
              <div className='flex justify-between'>
                <span className='text-gray-600 dark:text-gray-300'>
                  Shipping
                </span>
                <span className='font-semibold text-gray-900 dark:text-white'>
                  Free
                </span>
              </div>
              <div className='flex justify-between'>
                <span className='text-gray-600 dark:text-gray-300'>Tax</span>
                <span className='font-semibold text-gray-900 dark:text-white'>
                  ${(total * 0.08).toFixed(2)}
                </span>
              </div>
            </div>

            <div className='border-t border-gray-200 dark:border-gray-700 pt-4 mb-6'>
              <div className='flex justify-between'>
                <span className='text-lg font-bold text-gray-900 dark:text-white'>
                  Total
                </span>
                <span className='text-lg font-bold text-emerald-600 dark:text-emerald-400'>
                  ${(total * 1.08).toFixed(2)}
                </span>
              </div>
            </div>

            <button className='w-full bg-emerald-600 hover:bg-emerald-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 mb-4'>
              Proceed to Checkout
            </button>

            <Link
              to='/product'
              className='w-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center'
            >
              <ArrowLeft className='h-5 w-5 mr-2' />
              Continue Shopping
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Cart;
