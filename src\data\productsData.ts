import { images } from '@/utils/images';

export interface Product {
  id: string;
  name: string;
  category: string;
  description: string;
  price?: string;
  image: string;
  gallery?: string[];
  features?: string[];
  artisan?: string;
  materials?: string[];
  dimensions?: string;
  availability: 'available' | 'limited' | 'sold-out';
}

export interface ProductCategory {
  id: string;
  name: string;
  description: string;
  image: string;
  products: Product[];
}

// Individual Products Data
export const products: Product[] = [
  // Arts & Crafts Products
  {
    id: 'basket-001',
    name: 'Traditional Woven Basket',
    category: 'arts-crafts',
    description:
      'Handwoven basket made from natural materials using traditional techniques passed down through generations.',
    price: '$25 - $45',
    image: '/assets/baskets.jpg',
    gallery: ['/assets/baskets.jpg', '/assets/artdis1.jpg'],
    features: [
      'Handmade',
      'Natural Materials',
      'Traditional Design',
      'Durable',
    ],
    artisan: 'Grace Wanjiku',
    materials: ['Natural Fiber', 'Organic Dyes'],
    dimensions: '12" x 8" x 6"',
    availability: 'available',
  },
  {
    id: 'art-001',
    name: 'Cultural Art Piece',
    category: 'arts-crafts',
    description:
      'Beautiful cultural artwork representing the rich heritage and traditions of our community.',
    price: '$35 - $65',
    image: '/assets/art1.jpg',
    gallery: ['/assets/art1.jpg', '/assets/art3.jpg'],
    features: [
      'Cultural Heritage',
      'Unique Design',
      'Handcrafted',
      'Meaningful',
    ],
    artisan: 'Sarah Mukamana',
    materials: ['Canvas', 'Natural Pigments', 'Traditional Tools'],
    dimensions: '16" x 12"',
    availability: 'available',
  },
  {
    id: 'craft-001',
    name: 'Decorative Craft Set',
    category: 'arts-crafts',
    description:
      'Set of decorative crafts perfect for home decoration or as meaningful gifts.',
    price: '$20 - $40',
    image: '/assets/art3.jpg',
    gallery: ['/assets/art3.jpg', '/assets/artdis1.jpg'],
    features: ['Set of 3', 'Home Decoration', 'Gift Ready', 'Handmade'],
    artisan: 'Community Artisans',
    materials: ['Mixed Media', 'Natural Colors'],
    dimensions: 'Various sizes',
    availability: 'limited',
  },

  // Fashion & Textiles
  {
    id: 'garment-001',
    name: 'Traditional Dress',
    category: 'fashion',
    description:
      'Beautifully tailored traditional dress showcasing expert sewing and design skills.',
    price: '$45 - $85',
    image: '/assets/sewing.jpg',
    gallery: ['/assets/sewing.jpg', '/assets/training3.jpg'],
    features: [
      'Custom Tailored',
      'Traditional Design',
      'Quality Fabric',
      'Expert Craftsmanship',
    ],
    artisan: 'WOPEDE Tailoring Group',
    materials: ['Cotton', 'Traditional Patterns', 'Quality Thread'],
    dimensions: 'Custom Sizes Available',
    availability: 'available',
  },
  {
    id: 'textile-001',
    name: 'Handwoven Textile',
    category: 'fashion',
    description:
      'Handwoven textile piece perfect for clothing or home decoration.',
    price: '$30 - $55',
    image: '/assets/art1.jpg',
    gallery: ['/assets/art1.jpg', '/assets/training2.jpg'],
    features: ['Handwoven', 'Natural Fibers', 'Versatile Use', 'Durable'],
    artisan: 'Textile Cooperative',
    materials: ['Natural Fibers', 'Organic Dyes'],
    dimensions: '2m x 1.5m',
    availability: 'available',
  },

  // Beauty & Personal Care
  {
    id: 'beauty-001',
    name: 'Natural Hair Care Set',
    category: 'beauty',
    description:
      'Natural hair care products made with traditional ingredients and modern techniques.',
    price: '$15 - $30',
    image: '/assets/hair1.jpg',
    gallery: ['/assets/hair1.jpg', '/assets/hair2.jpg'],
    features: [
      'Natural Ingredients',
      'Traditional Recipe',
      'All Hair Types',
      'Chemical Free',
    ],
    artisan: 'Beauty Skills Group',
    materials: ['Natural Oils', 'Herbal Extracts', 'Organic Ingredients'],
    dimensions: 'Set of 3 bottles',
    availability: 'available',
  },

  // Food & Catering
  {
    id: 'food-001',
    name: 'Traditional Spice Blend',
    category: 'food',
    description:
      'Authentic spice blend prepared using traditional recipes and methods.',
    price: '$10 - $20',
    image: '/assets/bus2.jpg',
    gallery: ['/assets/bus2.jpg', '/assets/bus1.jpg'],
    features: [
      'Authentic Recipe',
      'Natural Spices',
      'Traditional Preparation',
      'Fresh',
    ],
    artisan: 'Catering Cooperative',
    materials: ['Natural Spices', 'Traditional Herbs'],
    dimensions: '250g package',
    availability: 'available',
  },

  // Colorful & Vibrant Products
  {
    id: 'colorful-bag-001',
    name: 'Vibrant Woven Handbag',
    category: 'arts-crafts',
    description:
      'Colorful handwoven bag with traditional patterns and modern appeal, perfect for everyday use.',
    price: '$35 - $65',
    image: '/assets/bus7.jpg',
    gallery: ['/assets/bus7.jpg', '/assets/bus8.jpg'],
    features: ['Handwoven', 'Colorful Design', 'Durable', 'Unique Patterns'],
    artisan: 'Grace Wanjiku',
    materials: ['Natural Fibers', 'Colorful Threads', 'Traditional Dyes'],
    dimensions: '12" x 10" x 4"',
    availability: 'available',
  },
  {
    id: 'beaded-jewelry-001',
    name: 'Traditional Beaded Jewelry Set',
    category: 'fashion',
    description:
      'Beautiful handcrafted beaded jewelry featuring vibrant colors and traditional designs.',
    price: '$25 - $45',
    image: '/assets/bus9.jpg',
    gallery: ['/assets/bus9.jpg', '/assets/artdis1.jpg'],
    features: [
      'Handbeaded',
      'Vibrant Colors',
      'Traditional Craft',
      'Complete Set',
    ],
    artisan: 'Sarah Mukamana',
    materials: ['Glass Beads', 'Natural Stones', 'Cotton Thread'],
    dimensions: 'Necklace: 18", Bracelet: 7"',
    availability: 'limited',
  },
  {
    id: 'decorative-bucket-001',
    name: 'Colorful Storage Bucket',
    category: 'arts-crafts',
    description:
      'Beautifully decorated storage bucket with vibrant colors and traditional motifs.',
    price: '$30 - $50',
    image: '/assets/bus5.jpg',
    gallery: ['/assets/bus5.jpg', '/assets/bus4.jpg'],
    features: [
      'Handpainted',
      'Functional Art',
      'Vibrant Colors',
      'Traditional Motifs',
    ],
    artisan: 'Community Artisans',
    materials: ['Wood', 'Natural Paints', 'Traditional Designs'],
    dimensions: '10" diameter x 12" height',
    availability: 'available',
  },
  {
    id: 'woven-mat-001',
    name: 'Colorful Woven Mat',
    category: 'arts-crafts',
    description:
      'Intricately woven mat with beautiful patterns and vibrant colors for home decoration.',
    price: '$40 - $70',
    image: '/assets/bus8.jpg',
    gallery: ['/assets/bus8.jpg', '/assets/baskets.jpg'],
    features: [
      'Handwoven',
      'Colorful Patterns',
      'Home Decor',
      'Traditional Weaving',
    ],
    artisan: 'Weaving Cooperative',
    materials: ['Natural Fibers', 'Organic Dyes', 'Traditional Techniques'],
    dimensions: '3ft x 5ft',
    availability: 'available',
  },
];

// Product Categories
export const productCategories: ProductCategory[] = [
  {
    id: 'arts-crafts',
    name: 'Arts & Crafts',
    description:
      'Handmade arts and crafts showcasing traditional techniques and cultural heritage.',
    image: images.arts.products.art1,
    products: products.filter((p) => p.category === 'arts-crafts'),
  },
  {
    id: 'fashion',
    name: 'Fashion & Textiles',
    description:
      'Beautiful clothing and textile products made with expert tailoring and design skills.',
    image: images.programs.training.sewing,
    products: products.filter((p) => p.category === 'fashion'),
  },
  {
    id: 'beauty',
    name: 'Beauty & Personal Care',
    description:
      'Natural beauty and personal care products using traditional ingredients.',
    image: images.beauty.services.hair1,
    products: products.filter((p) => p.category === 'beauty'),
  },
  {
    id: 'food',
    name: 'Food & Catering',
    description:
      'Traditional food products and catering services prepared with authentic recipes.',
    image: images.business.market.products,
    products: products.filter((p) => p.category === 'food'),
  },
];

// Featured Products - Colorful & Vibrant Selection
export const featuredProducts = products.filter((p) =>
  [
    'colorful-bag-001',
    'beaded-jewelry-001',
    'decorative-bucket-001',
    'woven-mat-001',
    'basket-001',
    'art-001',
  ].includes(p.id)
);

// Available Products
export const availableProducts = products.filter(
  (p) => p.availability === 'available'
);

// Product Statistics
export const productStats = {
  totalProducts: products.length,
  totalCategories: productCategories.length,
  availableProducts: availableProducts.length,
  artisans: [...new Set(products.map((p) => p.artisan))].length,
};
