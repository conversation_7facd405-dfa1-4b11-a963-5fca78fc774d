import { images } from '@/utils/images';

export interface Product {
  id: string;
  name: string;
  category: string;
  description: string;
  price?: string;
  image: string;
  gallery?: string[];
  features?: string[];
  artisan?: string;
  materials?: string[];
  dimensions?: string;
  availability: 'available' | 'limited' | 'sold-out';
}

export interface ProductCategory {
  id: string;
  name: string;
  description: string;
  image: string;
  products: Product[];
}

// Individual Products Data
export const products: Product[] = [
  // Arts & Crafts Products
  {
    id: 'basket-001',
    name: 'Traditional Woven Basket',
    category: 'arts-crafts',
    description: 'Handwoven basket made from natural materials using traditional techniques passed down through generations.',
    price: '$25 - $45',
    image: images.arts.products.baskets,
    gallery: [images.arts.products.baskets, images.arts.exhibition.display],
    features: ['Handmade', 'Natural Materials', 'Traditional Design', 'Durable'],
    artisan: 'Grace Wanjiku',
    materials: ['Natural Fiber', 'Organic Dyes'],
    dimensions: '12" x 8" x 6"',
    availability: 'available',
  },
  {
    id: 'art-001',
    name: 'Cultural Art Piece',
    category: 'arts-crafts',
    description: 'Beautiful cultural artwork representing the rich heritage and traditions of our community.',
    price: '$35 - $65',
    image: images.arts.products.art1,
    gallery: [images.arts.products.art1, images.arts.products.art3],
    features: ['Cultural Heritage', 'Unique Design', 'Handcrafted', 'Meaningful'],
    artisan: 'Sarah Mukamana',
    materials: ['Canvas', 'Natural Pigments', 'Traditional Tools'],
    dimensions: '16" x 12"',
    availability: 'available',
  },
  {
    id: 'craft-001',
    name: 'Decorative Craft Set',
    category: 'arts-crafts',
    description: 'Set of decorative crafts perfect for home decoration or as meaningful gifts.',
    price: '$20 - $40',
    image: images.arts.products.art3,
    gallery: [images.arts.products.art3, images.arts.exhibition.showcase],
    features: ['Set of 3', 'Home Decoration', 'Gift Ready', 'Handmade'],
    artisan: 'Community Artisans',
    materials: ['Mixed Media', 'Natural Colors'],
    dimensions: 'Various sizes',
    availability: 'limited',
  },

  // Fashion & Textiles
  {
    id: 'garment-001',
    name: 'Traditional Dress',
    category: 'fashion',
    description: 'Beautifully tailored traditional dress showcasing expert sewing and design skills.',
    price: '$45 - $85',
    image: images.programs.training.sewing,
    gallery: [images.programs.training.sewing, images.programs.vocational.tailoring],
    features: ['Custom Tailored', 'Traditional Design', 'Quality Fabric', 'Expert Craftsmanship'],
    artisan: 'WOPEDE Tailoring Group',
    materials: ['Cotton', 'Traditional Patterns', 'Quality Thread'],
    dimensions: 'Custom Sizes Available',
    availability: 'available',
  },
  {
    id: 'textile-001',
    name: 'Handwoven Textile',
    category: 'fashion',
    description: 'Handwoven textile piece perfect for clothing or home decoration.',
    price: '$30 - $55',
    image: images.programs.vocational.crafts,
    gallery: [images.programs.vocational.crafts, images.programs.training.workshop],
    features: ['Handwoven', 'Natural Fibers', 'Versatile Use', 'Durable'],
    artisan: 'Textile Cooperative',
    materials: ['Natural Fibers', 'Organic Dyes'],
    dimensions: '2m x 1.5m',
    availability: 'available',
  },

  // Beauty & Personal Care
  {
    id: 'beauty-001',
    name: 'Natural Hair Care Set',
    category: 'beauty',
    description: 'Natural hair care products made with traditional ingredients and modern techniques.',
    price: '$15 - $30',
    image: images.beauty.services.hair1,
    gallery: [images.beauty.services.hair1, images.beauty.services.hair2],
    features: ['Natural Ingredients', 'Traditional Recipe', 'All Hair Types', 'Chemical Free'],
    artisan: 'Beauty Skills Group',
    materials: ['Natural Oils', 'Herbal Extracts', 'Organic Ingredients'],
    dimensions: 'Set of 3 bottles',
    availability: 'available',
  },

  // Food & Catering
  {
    id: 'food-001',
    name: 'Traditional Spice Blend',
    category: 'food',
    description: 'Authentic spice blend prepared using traditional recipes and methods.',
    price: '$10 - $20',
    image: images.business.market.products,
    gallery: [images.business.market.products, images.business.development.bus2],
    features: ['Authentic Recipe', 'Natural Spices', 'Traditional Preparation', 'Fresh'],
    artisan: 'Catering Cooperative',
    materials: ['Natural Spices', 'Traditional Herbs'],
    dimensions: '250g package',
    availability: 'available',
  },
];

// Product Categories
export const productCategories: ProductCategory[] = [
  {
    id: 'arts-crafts',
    name: 'Arts & Crafts',
    description: 'Handmade arts and crafts showcasing traditional techniques and cultural heritage.',
    image: images.arts.products.art1,
    products: products.filter(p => p.category === 'arts-crafts'),
  },
  {
    id: 'fashion',
    name: 'Fashion & Textiles',
    description: 'Beautiful clothing and textile products made with expert tailoring and design skills.',
    image: images.programs.training.sewing,
    products: products.filter(p => p.category === 'fashion'),
  },
  {
    id: 'beauty',
    name: 'Beauty & Personal Care',
    description: 'Natural beauty and personal care products using traditional ingredients.',
    image: images.beauty.services.hair1,
    products: products.filter(p => p.category === 'beauty'),
  },
  {
    id: 'food',
    name: 'Food & Catering',
    description: 'Traditional food products and catering services prepared with authentic recipes.',
    image: images.business.market.products,
    products: products.filter(p => p.category === 'food'),
  },
];

// Featured Products
export const featuredProducts = products.filter(p => 
  ['basket-001', 'garment-001', 'beauty-001', 'art-001'].includes(p.id)
);

// Available Products
export const availableProducts = products.filter(p => p.availability === 'available');

// Product Statistics
export const productStats = {
  totalProducts: products.length,
  totalCategories: productCategories.length,
  availableProducts: availableProducts.length,
  artisans: [...new Set(products.map(p => p.artisan))].length,
};
