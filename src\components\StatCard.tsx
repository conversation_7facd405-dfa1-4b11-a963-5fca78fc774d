
import React from 'react';
import AnimatedSection from './AnimatedSection';

interface StatCardProps {
  number: string;
  title: string;
  description: string;
  delay?: number;
}

const StatCard: React.FC<StatCardProps> = ({ number, title, description, delay = 0 }) => {
  return (
    <AnimatedSection delay={delay} animation="fade-in">
      <div className="text-center group hover:scale-105 transition-all duration-700 bg-white/60 backdrop-blur-sm rounded-3xl p-10 border border-white/40 shadow-lg hover:shadow-xl">
        <div className="text-6xl sm:text-7xl font-bold bg-gradient-to-r from-amber-500 to-yellow-500 bg-clip-text text-transparent mb-4 group-hover:animate-pulse">
          {number}
        </div>
        <div className="text-xl font-bold text-gray-800 mb-2">
          {title}
        </div>
        <div className="text-gray-600 font-light">
          {description}
        </div>
      </div>
    </AnimatedSection>
  );
};

export default React.memo(StatCard);
