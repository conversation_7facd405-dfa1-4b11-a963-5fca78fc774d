// Image imports from public/assets directory
export const images = {
  // ===== LOGO & BRANDING =====
  logo: {
    main: '/assets/logo.png', // Main WOPEDE logo
    white: '/assets/logo-white.png', // White version for dark backgrounds
    dark: '/assets/logo-dark.png', // Dark version for light backgrounds
    icon: '/assets/logo-icon.png', // Icon only version
    full: '/assets/logo-full.png', // Logo with full text
    favicon: '/assets/favicon.ico', // Favicon
    symbol: '/assets/logo-symbol.png', // Symbol/mark only
  },

  // ===== HERO & MAIN BRANDING =====
  hero: {
    main: '/assets/founder.jpg',
    background: '/assets/bg.png',
    bg: '/assets/bg.png',
    alternative: '/assets/1m.jpg',
    waving: '/assets/waving.jpg',
  },

  // ===== FOUNDER & LEADERSHIP =====
  founder: {
    main: '/assets/founder1.jpg',
    modesta: '/assets/modesta.jpg',
    portrait: '/assets/founder.jpg',
    speaking: '/assets/waving.jpg',
  },

  // ===== PROGRAMS & TRAINING =====
  programs: {
    // Skills Training
    training: {
      general1: '/assets/training1.jpg',
      general2: '/assets/training2.jpg',
      general3: '/assets/training3.jpg',
      sewing: '/assets/sewing.jpg',
      classroom: '/assets/training1.jpg', // Can be updated with specific classroom image
      workshop: '/assets/training2.jpg',
    },

    // Vocational Skills
    vocational: {
      sewing: '/assets/sewing.jpg',
      tailoring: '/assets/training3.jpg',
      crafts: '/assets/art1.jpg',
      business: '/assets/bus1.jpg',
    },

    // Life Skills & Counseling
    counseling: {
      session: '/assets/training2.jpg', // Can be updated with counseling session image
      group: '/assets/training1.jpg',
      support: '/assets/training3.jpg',
    },
  },

  // ===== BUSINESS & ENTREPRENEURSHIP =====
  business: {
    // Small Business Development
    development: {
      bus1: '/assets/bus1.jpg',
      bus2: '/assets/bus2.jpg',
      bus3: '/assets/bus3.jpg',
      bus4: '/assets/bus4.jpg',
    },

    // Success Stories
    success: {
      bus5: '/assets/bus5.jpg',
      bus7: '/assets/bus7.jpg',
      bus8: '/assets/bus8.jpg',
      bus9: '/assets/bus9.jpg',
    },

    // Market & Sales
    market: {
      selling: '/assets/bus1.jpg',
      products: '/assets/bus2.jpg',
      display: '/assets/artdis1.jpg',
    },
  },

  // ===== ARTS & CRAFTS =====
  arts: {
    // Handmade Products
    products: {
      art1: '/assets/art1.jpg',
      art3: '/assets/art3.jpg',
      baskets: '/assets/baskets.jpg',
      display: '/assets/artdis1.jpg',
    },

    // Craft Training
    training: {
      basketWeaving: '/assets/baskets.jpg',
      artMaking: '/assets/art1.jpg',
      craftsWorkshop: '/assets/art3.jpg',
    },

    // Exhibition & Sales
    exhibition: {
      display: '/assets/artdis1.jpg',
      showcase: '/assets/art3.jpg',
    },
  },

  // ===== BEAUTY & PERSONAL CARE =====
  beauty: {
    // Hair & Beauty Services
    services: {
      hair1: '/assets/hair1.jpg',
      hair2: '/assets/hair2.jpg',
      styling: '/assets/hair1.jpg',
      treatment: '/assets/hair2.jpg',
    },

    // Training
    training: {
      hairTraining: '/assets/hair1.jpg',
      beautySkills: '/assets/hair2.jpg',
    },
  },

  // ===== COMMUNITY & IMPACT =====
  community: {
    // Community Events
    events: {
      gathering: '/assets/stakeholders.jpg',
      meeting: '/assets/training1.jpg',
      celebration: '/assets/waving.jpg',
    },

    // Beneficiaries
    beneficiaries: {
      women: '/assets/training2.jpg',
      group: '/assets/training1.jpg',
      families: '/assets/stakeholders.jpg',
    },

    // Locations
    locations: {
      map: '/assets/map.png',
      facility: '/assets/bg.png',
      workspace: '/assets/training3.jpg',
    },
  },

  // ===== PARTNERSHIPS & STAKEHOLDERS =====
  partners: {
    // International Partners
    international: {
      inkomoko: '/assets/Inkomoko.png',
      refugePoint: '/assets/RefugePoint.jpg',
      unhcr: '/assets/Unhcr.png',
    },

    // Local Partners
    local: {
      pLg: '/assets/P-lg.png',
    },

    // Stakeholder Events
    events: {
      stakeholders: '/assets/stakeholders.jpg',
      meetings: '/assets/stakeholders.jpg',
    },
  },

  // ===== BACKGROUNDS & DECORATIVE =====
  backgrounds: {
    // Page Backgrounds
    main: '/assets/bg.png',
    hero: '/assets/1m.jpg',
    pattern: '/assets/bg.png',

    // Section Backgrounds
    about: '/assets/bg.png',
    programs: '/assets/1m.jpg',
    impact: '/assets/bg.png',
  },

  // ===== LEGACY REFERENCES (for backward compatibility) =====
  // Keep existing references for components already using them
  training: {
    training1: '/assets/training1.jpg',
    training2: '/assets/training2.jpg',
    training3: '/assets/training3.jpg',
    sewing: '/assets/sewing.jpg',
  },

  // Legacy individual references
  stakeholders: '/assets/stakeholders.jpg',
  map: '/assets/map.png',
  bg: '/assets/bg.png',
  oneM: '/assets/1m.jpg',
};
