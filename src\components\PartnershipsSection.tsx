import React from 'react';
import {
  Users,
  Award,
  Globe,
  Handshake,
  Target,
  Building,
  Heart,
  ArrowRight,
} from 'lucide-react';
import PartnersSection from './PartnersSection';

const PartnershipsSection = () => {
  const partners = [
    {
      name: 'Pamoja Trust',
      description:
        'Supporting community development and empowerment initiatives across Kenya.',
      focus: 'Community Development',
      icon: Users,
      color: 'from-blue-500 to-blue-600',
      activities: [
        'Community mobilization',
        'Leadership training',
        'Local partnerships',
        'Capacity building',
      ],
    },
    {
      name: 'Inkomoko',
      description:
        'Providing business training and financial services to entrepreneurs in East Africa.',
      focus: 'Business Training',
      icon: Target,
      color: 'from-green-500 to-green-600',
      activities: [
        'Business training',
        'Microfinance',
        'Mentorship programs',
        'Market linkages',
      ],
    },
    {
      name: 'Cohere',
      description:
        'Fostering social cohesion and integration in refugee and host communities.',
      focus: 'Social Cohesion',
      icon: Handshake,
      color: 'from-purple-500 to-purple-600',
      activities: [
        'Community dialogue',
        'Conflict resolution',
        'Cultural exchange',
        'Integration programs',
      ],
    },
    {
      name: 'Refugee Point',
      description:
        'Connecting refugees with opportunities and resources for sustainable livelihoods.',
      focus: 'Refugee Support',
      icon: Globe,
      color: 'from-indigo-500 to-indigo-600',
      activities: [
        'Resource mapping',
        'Livelihood support',
        'Network building',
        'Advocacy work',
      ],
    },
    {
      name: 'JRS (Jesuit Refugee Service)',
      description:
        'Serving, accompanying, and advocating for the rights of refugees and displaced persons.',
      focus: 'Refugee Services',
      icon: Award,
      color: 'from-amber-500 to-amber-600',
      activities: [
        'Rights advocacy',
        'Legal support',
        'Psychosocial support',
        'Protection services',
      ],
    },
  ];

  const opportunities = [
    {
      title: 'Volunteer Partnership',
      description:
        'Share your expertise and time to support our programs and initiatives.',
      icon: Heart,
      color: 'from-rose-500 to-pink-500',
      benefits: ['Skill sharing', 'Community impact', 'Personal growth'],
    },
    {
      title: 'Program Sponsorship',
      description:
        'Support specific training programs or sponsor individual participants.',
      icon: Award,
      color: 'from-emerald-500 to-teal-500',
      benefits: ['Direct impact', 'Progress tracking', 'Recognition'],
    },
    {
      title: 'Strategic Collaboration',
      description:
        'Partner with us to expand reach and amplify impact in communities.',
      icon: Building,
      color: 'from-violet-500 to-purple-500',
      benefits: ['Joint initiatives', 'Shared resources', 'Greater reach'],
    },
  ];

  return (
    <div className='bg-gradient-to-br from-gray-50 to-white dark:from-gray-800 dark:to-gray-900 rounded-2xl px-2 py-4 shadow-2xl border border-gray-100 dark:border-gray-700'>
      <div className='text-center mb-12'>
        <h2 className='text-3xl md:text-4xl font-bold text-gray-800 dark:text-white mb-4 gradient-text-hero'>
          Partnerships & Collaboration
        </h2>
        <div className='w-20 h-1 bg-gradient-to-r from-amber-500 to-yellow-500 mx-auto mb-4 rounded-full'></div>
        <p className='text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto leading-relaxed'>
          Building stronger communities through strategic partnerships and
          meaningful collaboration opportunities.
        </p>
      </div>

      {/* Our Partners Section */}
      <div className='mb-16'>
        <div className='text-center mb-8'>
          <div className='flex items-center justify-center mb-4'>
            <Handshake className='w-6 h-6 text-navy-800 mr-3' />
            <h3 className='text-2xl font-bold text-gray-800 dark:text-white'>
              Our Partners
            </h3>
          </div>
        </div>

        {/* <div className='grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6'>
          {partners.map((partner, index) => {
            const IconComponent = partner.icon;
            return (
              <div
                key={index}
                className='bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group overflow-hidden'
              >
                <div className='relative h-20 overflow-hidden'>
                  <div
                    className={`w-full h-full bg-gradient-to-r ${partner.color} flex items-center justify-center`}
                  >
                    <IconComponent className='w-8 h-8 text-white/80' />
                  </div>
                </div>

                <div className='p-4'>
                  <span className='text-xs text-amber-700 dark:text-amber-300 font-semibold bg-amber-100 dark:bg-amber-900/30 px-2 py-1 rounded-full inline-block mb-2'>
                    {partner.focus}
                  </span>

                  <h4 className='text-lg font-bold text-gray-800 dark:text-white mb-2 group-hover:text-amber-600 dark:group-hover:text-amber-400 transition-colors duration-300'>
                    {partner.name}
                  </h4>

                  <p className='text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2'>
                    {partner.description}
                  </p>
                </div>
              </div>
            );
          })}
        </div> */}
        <PartnersSection
          variant='featured'
          maxPartners={4}
          backgroundColor='bg-gradient-to-br from-navy-50 via-white to-gold-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900'
        />
      </div>

      {/* Partnership Opportunities Section */}
      <div className='space-y-6'>
        <div className='text-center mb-8'>
          <div className='flex items-center justify-center mb-4'>
            <Target className='w-6 h-6 text-amber-600 mr-3' />
            <h3 className='text-2xl font-bold text-gray-800 dark:text-white'>
              Partnership Opportunities
            </h3>
          </div>
        </div>

        <div className='grid md:grid-cols-3 gap-6'>
          {opportunities.map((opportunity, index) => {
            const IconComponent = opportunity.icon;
            return (
              <div
                key={index}
                className='bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-600'
              >
                <div className='flex items-start mb-4'>
                  <div
                    className={`w-10 h-10 bg-gradient-to-r ${opportunity.color} rounded-lg flex items-center justify-center mr-4 shadow-md`}
                  >
                    <IconComponent className='w-5 h-5 text-white' />
                  </div>
                  <div className='flex-1'>
                    <h4 className='text-lg font-semibold text-gray-800 dark:text-white mb-2'>
                      {opportunity.title}
                    </h4>
                    <p className='text-gray-600 dark:text-gray-400 text-sm mb-3 leading-relaxed'>
                      {opportunity.description}
                    </p>
                    <div className='flex flex-wrap gap-2'>
                      {opportunity.benefits.map((benefit, idx) => (
                        <span
                          key={idx}
                          className='text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded-full'
                        >
                          {benefit}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
                <a
                  href='#contact'
                  className='text-navy-800 dark:text-navy-400 font-medium text-sm hover:text-navy-900 dark:hover:text-navy-300 transition-colors'
                >
                  Learn More →
                </a>
              </div>
            );
          })}
        </div>
      </div>

      <div className='text-center mt-12'>
        <a
          href='#contact'
          className='inline-block bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 text-white px-8 py-3 rounded-xl font-bold text-base transition-all duration-300 transform hover:scale-105 shadow-lg'
        >
          Explore Partnership Opportunities
        </a>
      </div>
    </div>
  );
};

export { PartnershipsSection };
