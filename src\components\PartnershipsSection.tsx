
import React from 'react';
import { Users, Award, Globe, Handshake, Target, Building, Heart } from 'lucide-react';

const PartnershipsSection = () => {
  const partners = [
    {
      name: 'Pamoja Trust',
      description: 'Supporting community development and empowerment initiatives across Kenya.',
      focus: 'Community Development',
      icon: Users,
      color: 'from-blue-500 to-blue-600'
    },
    {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      description: 'Providing business training and financial services to entrepreneurs in East Africa.',
      focus: 'Business Training',
      icon: Target,
      color: 'from-green-500 to-green-600'
    },
    {
      name: 'Cohere',
      description: 'Fostering social cohesion and integration in refugee and host communities.',
      focus: 'Social Cohesion',
      icon: Handshake,
      color: 'from-purple-500 to-purple-600'
    },
    {
      name: 'Refugee Point',
      description: 'Connecting refugees with opportunities and resources for sustainable livelihoods.',
      focus: 'Refugee Support',
      icon: Globe,
      color: 'from-indigo-500 to-indigo-600'
    },
    {
      name: 'JRS (Jesuit Refugee Service)',
      description: 'Serving, accompanying, and advocating for the rights of refugees and displaced persons.',
      focus: 'Refugee Services',
      icon: Award,
      color: 'from-amber-500 to-amber-600'
    },
  ];

  const opportunities = [
    {
      title: 'Volunteer Partnership',
      description: 'Share your expertise and time to support our programs and initiatives.',
      icon: Heart,
      color: 'from-rose-500 to-pink-500',
      benefits: ['Skill sharing', 'Community impact', 'Personal growth']
    },
    {
      title: 'Program Sponsorship',
      description: 'Support specific training programs or sponsor individual participants.',
      icon: Award,
      color: 'from-emerald-500 to-teal-500',
      benefits: ['Direct impact', 'Progress tracking', 'Recognition']
    },
    {
      title: 'Strategic Collaboration',
      description: 'Partner with us to expand reach and amplify impact in communities.',
      icon: Building,
      color: 'from-violet-500 to-purple-500',
      benefits: ['Joint initiatives', 'Shared resources', 'Greater reach']
    },
  ];

  return (
    <div className='bg-gradient-to-br from-gray-50 to-white dark:from-gray-800 dark:to-gray-900 rounded-3xl p-10 shadow-2xl border border-gray-100 dark:border-gray-700'>
      <div className='text-center mb-12'>
        <h2 className='text-3xl md:text-4xl font-bold text-gray-800 dark:text-white mb-4 gradient-text-hero'>
          Partnerships & Collaboration
        </h2>
        <div className='w-20 h-1 bg-gradient-to-r from-amber-500 to-yellow-500 mx-auto mb-4 rounded-full'></div>
        <p className='text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto leading-relaxed'>
          Building stronger communities through strategic partnerships and meaningful collaboration opportunities.
        </p>
      </div>

      <div className='grid lg:grid-cols-2 gap-12'>
        {/* Our Partners Section */}
        <div className='space-y-6'>
          <div className='text-center mb-8'>
            <div className='flex items-center justify-center mb-4'>
              <Handshake className='w-6 h-6 text-navy-800 mr-3' />
              <h3 className='text-2xl font-bold text-gray-800 dark:text-white'>Our Partners</h3>
            </div>
            <p className='text-gray-600 dark:text-gray-400'>
              Trusted organizations working alongside us to create lasting change.
            </p>
          </div>

          <div className='space-y-4'>
            {partners.map((partner, index) => (
              <div
                key={index}
                className='group bg-white dark:bg-gray-800 hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 rounded-xl p-5'
              >
                <div className='flex items-start'>
                  <div className={`w-10 h-10 bg-gradient-to-r ${partner.color} rounded-lg flex items-center justify-center mr-4 flex-shrink-0 shadow-md`}>
                    <partner.icon className='w-5 h-5 text-white' />
                  </div>
                  <div className='flex-1'>
                    <div className='flex items-center mb-2'>
                      <h4 className='text-lg font-semibold text-gray-800 dark:text-white mr-3'>
                        {partner.name}
                      </h4>
                      <span className='text-xs text-navy-700 dark:text-navy-300 font-medium bg-navy-100 dark:bg-navy-800 px-3 py-1 rounded-full'>
                        {partner.focus}
                      </span>
                    </div>
                    <p className='text-gray-600 dark:text-gray-400 text-sm leading-relaxed'>
                      {partner.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className='text-center mt-6'>
            <p className='text-sm text-gray-600 dark:text-gray-400 mb-4'>
              Interested in becoming a partner?
            </p>
            <a
              href='#contact'
              className='inline-block bg-navy-800 hover:bg-navy-900 text-white px-6 py-3 rounded-xl font-semibold text-sm transition-all duration-300 transform hover:scale-105 shadow-lg'
            >
              Partner With Us
            </a>
          </div>
        </div>

        {/* Partnership Opportunities Section */}
        <div className='space-y-6'>
          <div className='text-center mb-8'>
            <div className='flex items-center justify-center mb-4'>
              <Target className='w-6 h-6 text-amber-600 mr-3' />
              <h3 className='text-2xl font-bold text-gray-800 dark:text-white'>Partnership Opportunities</h3>
            </div>
            <p className='text-gray-600 dark:text-gray-400'>
              Multiple ways to join our mission and make a meaningful impact.
            </p>
          </div>

          <div className='space-y-4'>
            {opportunities.map((opportunity, index) => (
              <div key={index} className='bg-white dark:bg-gray-800 p-6 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300 hover:border-gray-300 dark:hover:border-gray-600'>
                <div className='flex items-start mb-4'>
                  <div className={`w-10 h-10 bg-gradient-to-r ${opportunity.color} rounded-lg flex items-center justify-center mr-4 shadow-md`}>
                    <opportunity.icon className='w-5 h-5 text-white' />
                  </div>
                  <div className='flex-1'>
                    <h4 className='text-lg font-semibold text-gray-800 dark:text-white mb-2'>
                      {opportunity.title}
                    </h4>
                    <p className='text-gray-600 dark:text-gray-400 text-sm mb-3 leading-relaxed'>
                      {opportunity.description}
                    </p>
                    <div className='flex flex-wrap gap-2'>
                      {opportunity.benefits.map((benefit, idx) => (
                        <span key={idx} className='text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded-full'>
                          {benefit}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
                <a
                  href='#contact'
                  className='text-navy-800 dark:text-navy-400 font-medium text-sm hover:text-navy-900 dark:hover:text-navy-300 transition-colors'
                >
                  Learn More →
                </a>
              </div>
            ))}
          </div>

          <div className='text-center mt-6'>
            <a
              href='#contact'
              className='inline-block bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 text-white px-8 py-3 rounded-xl font-bold text-base transition-all duration-300 transform hover:scale-105 shadow-lg'
            >
              Explore Partnership
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export { PartnershipsSection };
