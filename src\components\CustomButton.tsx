import React from 'react';
import { Link } from 'react-router-dom';
import { LucideIcon } from 'lucide-react';

export interface CustomButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'gradient' | 'transparent';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  onClick?: () => void;
  href?: string;
  to?: string;
  disabled?: boolean;
  icon?: LucideIcon;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  loading?: boolean;
}

const CustomButton: React.FC<CustomButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  className = '',
  onClick,
  href,
  to,
  disabled = false,
  icon: Icon,
  iconPosition = 'left',
  fullWidth = false,
  loading = false,
}) => {
  // Base styles - consistent across all buttons
  const baseStyles = `
    font-serif inline-flex items-center justify-center font-semibold
    transition-all duration-300 transform hover:scale-105 hover:-translate-y-0.5
    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
    disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none
    shadow-lg hover:shadow-xl
  `;

  // Variant styles
  const variantStyles = {
    primary: `
      bg-gradient-to-r from-blue-600 to-indigo-600 text-white
      hover:from-blue-700 hover:to-indigo-700
      shadow-blue-500/25 hover:shadow-blue-500/40
    `,
    secondary: `
      bg-slate-800 text-white
      hover:bg-slate-700
      shadow-slate-500/25 hover:shadow-slate-500/40
    `,
    outline: `
      bg-transparent border-2 border-blue-600 text-blue-600
      hover:bg-blue-600 hover:text-white
      shadow-blue-500/25 hover:shadow-blue-500/40
    `,
    ghost: `
      bg-white/90 backdrop-blur-sm text-slate-800 border border-slate-200
      hover:bg-white hover:shadow-slate-500/25
      dark:bg-slate-800/90 dark:text-white dark:border-slate-600
      dark:hover:bg-slate-800
    `,
    gradient: `
      bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 text-white
      hover:from-purple-700 hover:via-blue-700 hover:to-indigo-700
      shadow-purple-500/25 hover:shadow-purple-500/40
    `,
    transparent: `
      bg-white/10 backdrop-blur-md border border-white/20 text-white
      hover:bg-white/20 hover:border-white/30
      shadow-white/10 hover:shadow-white/20
    `,
  };

  // Size styles
  const sizeStyles = {
    sm: 'px-4 py-2 text-sm rounded-xl',
    md: 'px-6 py-3 text-base rounded-xl',
    lg: 'px-8 py-4 text-lg rounded-2xl',
    xl: 'px-10 py-5 text-xl rounded-2xl',
  };

  // Combine all styles
  const combinedStyles = `
    ${baseStyles}
    ${variantStyles[variant]}
    ${sizeStyles[size]}
    ${fullWidth ? 'w-full' : ''}
    ${className}
  `;

  // Icon spacing
  const iconSpacing = size === 'sm' ? 'space-x-2' : size === 'md' ? 'space-x-2' : 'space-x-3';
  const iconSize = size === 'sm' ? 'w-4 h-4' : size === 'md' ? 'w-5 h-5' : 'w-6 h-6';

  const content = (
    <>
      {loading && (
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
      )}
      {Icon && iconPosition === 'left' && <Icon className={iconSize} />}
      <span className="relative">{children}</span>
      {Icon && iconPosition === 'right' && <Icon className={iconSize} />}
    </>
  );

  // Render as Link if 'to' prop is provided
  if (to) {
    return (
      <Link
        to={to}
        className={`${combinedStyles} ${iconSpacing}`}
        onClick={onClick}
      >
        {content}
      </Link>
    );
  }

  // Render as anchor if 'href' prop is provided
  if (href) {
    return (
      <a
        href={href}
        className={`${combinedStyles} ${iconSpacing}`}
        onClick={onClick}
        target="_blank"
        rel="noopener noreferrer"
      >
        {content}
      </a>
    );
  }

  // Render as button
  return (
    <button
      className={`${combinedStyles} ${iconSpacing}`}
      onClick={onClick}
      disabled={disabled || loading}
    >
      {content}
    </button>
  );
};

export default CustomButton;
