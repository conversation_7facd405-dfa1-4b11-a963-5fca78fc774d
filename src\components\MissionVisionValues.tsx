import React from 'react';
import {
  Target,
  Sparkles,
  Heart,
  Users,
  Lightbulb,
  Globe,
  HandHeart,
} from 'lucide-react';
import AnimatedSection from './AnimatedSection';
import { Link } from 'react-router-dom';

const MissionVisionValues: React.FC = () => {
  const values = [
    {
      title: 'Empowerment',
      description: 'Building confidence and independence in women.',
      icon: <Lightbulb className='w-5 h-5' />,
    },
    {
      title: 'Resilience',
      description: 'Inspiring strength in the face of adversity.',
      icon: <Heart className='w-5 h-5' />,
    },
    {
      title: 'Collaboration',
      description: 'Partnering with communities and stakeholders.',
      icon: <Users className='w-5 h-5' />,
    },
    {
      title: 'Sustainability',
      description: 'Encouraging long-term growth and self-reliance.',
      icon: <Target className='w-5 h-5' />,
    },
    {
      title: 'Compassion',
      description: 'Providing a safe and supportive environment.',
      icon: <HandHeart className='w-5 h-5' />,
    },
  ];

  return (
    <section className='py-20 bg-gradient-to-br from-navy-50 via-white to-gold-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900'>
      <div className='container mx-auto px-6'>
        <AnimatedSection delay={0}>
          <div className='bg-gradient-to-br from-white/80 to-white/60 dark:from-navy-800/90 dark:to-navy-900/80 backdrop-blur-sm rounded-3xl p-8 lg:p-12 shadow-2xl border border-white/20 dark:border-gray-700/20'>
            {/* Header */}
            <div className='text-center mb-16'>
              <AnimatedSection delay={200}>
                <h2 className='text-4xl lg:text-5xl font-bold text-navy-800 dark:text-white mb-6'>
                  Our Foundation
                </h2>
                <div className='w-24 h-1 bg-gradient-to-r from-gold-500 to-navy-600 mx-auto mb-6 rounded-full'></div>
                <p className='text-xl text-gray-700 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed font-light'>
                  Built on three pillars that guide every decision, every
                  program, and every life we touch.
                </p>
              </AnimatedSection>
            </div>

            {/* Mission, Vision, Values Grid */}
            <div className='grid lg:grid-cols-3 gap-8 mb-12'>
              {/* Mission */}

              <AnimatedSection delay={300}>
                <div className='bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-xl border border-gold-200 dark:border-navy-700'>
                  <div className='flex items-center mb-6'>
                    <div className='w-16 h-16 bg-gradient-to-r from-navy-500 to-navy-600 rounded-2xl flex items-center justify-center mb-6'>
                      <Target className='w-8 h-8 text-white' />
                    </div>
                    <h3 className='text-2xl font-bold text-navy-800 dark:text-white mb-4'>
                      Our Mission
                    </h3>
                  </div>
                  <p className='text-gray-700 dark:text-gray-300 leading-relaxed'>
                    WOPEDE CBO aims to empower women through practical skills,
                    emotional support, and resources that enable them to become
                    self-reliant and agents of peace in their communities. We
                    believe in the transformative power of women to create
                    strong families and harmonious societies.
                  </p>
                </div>
              </AnimatedSection>
              <AnimatedSection delay={600}>
                <div className='bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-xl border border-gold-200 dark:border-gold-700'>
                  <div className='flex items-center mb-6'>
                    <div className='w-16 h-16 bg-gradient-to-r from-gold-500 to-gold-600 rounded-2xl flex items-center justify-center mb-6'>
                      <Globe className='w-8 h-8 text-white' />
                    </div>
                    <h3 className='text-2xl font-bold text-navy-800 dark:text-white mb-4'>
                      Our Vision
                    </h3>
                  </div>
                  <p className='text-gray-700 dark:text-gray-300 leading-relaxed'>
                    WOPEDE envisions a world where women lead dignified lives,
                    support their families effectively, and act as ambassadors
                    of peace. We strive to break cycles of poverty and conflict
                    by fostering resilience, growth, and harmony.
                  </p>
                </div>
              </AnimatedSection>

              {/* Values */}
              <AnimatedSection delay={800}>
                <div className='bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-xl border border-gold-200 dark:border-gold-700'>
                  {/* Horizontal Icon and Title */}
                  <div className='flex items-center mb-6'>
                    <div className='w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300 shadow-lg'>
                      <Heart className='w-6 h-6 text-white' />
                    </div>
                    <h3 className='text-2xl font-bold text-navy-800 dark:text-white'>
                      Our Values
                    </h3>
                  </div>
                  <div className='space-y-4'>
                    {values.map((value, index) => (
                      <div key={index} className='flex items-start group/item'>
                        <div className='w-6 h-6 bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center mr-3 flex-shrink-0 group-hover/item:scale-110 transition-transform duration-300 shadow-sm'>
                          {value.icon}
                        </div>
                        <div>
                          <span className='font-semibold text-green-800 dark:text-green-400'>
                            {value.title}:
                          </span>
                          <span className='text-gray-700 dark:text-gray-300 text-sm ml-1'>
                            {value.description}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </AnimatedSection>
            </div>

            {/* Call to Action */}
            <AnimatedSection delay={1000}>
              <div className='text-center'>
                <Link
                  to='/contact'
                  className='group inline-flex items-center space-x-3 bg-gradient-to-r from-navy-800 to-navy-600 text-white px-10 py-5 rounded-2xl font-semibold text-lg hover:from-navy-700 hover:to-navy-500 transition-all duration-300 transform hover:scale-105 shadow-2xl hover:shadow-3xl'
                >
                  <span>Join Our Mission</span>
                  <div className='w-0 group-hover:w-6 h-0.5 bg-white transition-all duration-300'></div>
                  <span className='transform group-hover:translate-x-1 transition-transform duration-300'>
                    →
                  </span>
                </Link>
              </div>
            </AnimatedSection>
          </div>
        </AnimatedSection>
      </div>
    </section>
  );
};

export default React.memo(MissionVisionValues);
