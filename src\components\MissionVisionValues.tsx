import React from 'react';
import {
  Target,
  Sparkles,
  Heart,
  Users,
  Lightbulb,
  HandHeart,
} from 'lucide-react';
import AnimatedSection from './AnimatedSection';
import { Link } from 'react-router-dom';

const MissionVisionValues: React.FC = () => {
  const values = [
    {
      title: 'Empowerment',
      description: 'Building confidence and independence in women.',
      icon: <Lightbulb className='w-5 h-5' />,
    },
    {
      title: 'Resilience',
      description: 'Inspiring strength in the face of adversity.',
      icon: <Heart className='w-5 h-5' />,
    },
    {
      title: 'Collaboration',
      description: 'Partnering with communities and stakeholders.',
      icon: <Users className='w-5 h-5' />,
    },
    {
      title: 'Sustainability',
      description: 'Encouraging long-term growth and self-reliance.',
      icon: <Target className='w-5 h-5' />,
    },
    {
      title: 'Compassion',
      description: 'Providing a safe and supportive environment.',
      icon: <HandHeart className='w-5 h-5' />,
    },
  ];

  return (
    <section className='py-32 bg-gradient-to-br from-slate-50 via-blue-50/30 to-sky-100/50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900'>
      <div className='container mx-auto px-2 lg:px-4 l'>
        <AnimatedSection delay={0}>
          <div className='bg-gradient-to-br from-white/98 to-slate-50/90 dark:from-slate-800/98  relative overflow-hidden'>
            {/* Subtle background pattern */}
            <div className='absolute inset-0 opacity-[0.02] bg-gradient-to-br from-blue-900 via-transparent to-blue-900'></div>
            {/* Header */}
            {/* Enhanced Header */}
            <div className='relative text-center mb-20'>
              <AnimatedSection delay={200}>
                <div className='mb-8'>
                  <h2 className='text-5xl lg:text-6xl xl:text-7xl font-black text-slate-800 dark:text-white mb-6 tracking-tight leading-none'>
                    Our Foundation
                  </h2>
                  <div className='w-32 h-2 bg-gradient-to-r from-blue-500 via-sky-400 to-indigo-500 mx-auto mb-8 rounded-full shadow-lg'></div>
                </div>
                <p className='text-2xl lg:text-3xl text-slate-600 dark:text-gray-300 max-w-4xl mx-auto leading-relaxed font-light tracking-wide'>
                  Built on three pillars that guide every decision, every
                  program, and every life we touch.
                </p>
                <div className='mt-8 flex justify-center'>
                  <div className='flex items-center space-x-2 text-slate-500 dark:text-gray-400'>
                    <div className='w-2 h-2 bg-blue-500 rounded-full animate-pulse'></div>
                    <span className='text-sm font-medium tracking-wider uppercase'>
                      Core Values
                    </span>
                    <div className='w-2 h-2 bg-blue-500 rounded-full animate-pulse'></div>
                  </div>
                </div>
              </AnimatedSection>
            </div>

            {/* Mission, Vision, Values Grid */}
            <div className='grid lg:grid-cols-3 gap-5 mb-12'>
              {/* Mission */}

              <AnimatedSection delay={400}>
                <div className='group relative bg-gradient-to-br from-white via-blue-50/30 to-slate-50 dark:from-slate-800 dark:via-blue-900/10 dark:to-slate-900 p-10 rounded-3xl shadow-2xl hover:shadow-3xl transition-all duration-700 transform hover:-translate-y-4 hover:scale-105 border border-slate-200/50 dark:border-slate-700/50 overflow-hidden'>
                  {/* Subtle glow effect */}
                  <div className='absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500'></div>

                  {/* Horizontal Icon and Title */}
                  <div className='relative flex items-center mb-8'>
                    <div className='w-16 h-16 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mr-6 group-hover:scale-110 transition-transform duration-500 shadow-xl'>
                      <Target className='w-8 h-8 text-white' />
                    </div>
                    <h3 className='text-3xl font-black text-slate-800 dark:text-white tracking-tight'>
                      Our Mission
                    </h3>
                  </div>

                  <p className='relative text-lg text-slate-600 dark:text-gray-300 leading-relaxed font-light'>
                    WOPEDE CBO aims to empower women through practical skills,
                    emotional support, and resources that enable them to become
                    self-reliant and agents of peace in their communities. We
                    believe in the transformative power of women to create
                    strong families and harmonious societies.
                  </p>

                  {/* Decorative accent */}
                  <div className='relative mt-6 w-full h-0.5 bg-gradient-to-r from-blue-500/50 to-transparent group-hover:from-blue-500 transition-all duration-500'></div>
                </div>
              </AnimatedSection>
              <AnimatedSection delay={600}>
                <div className='group relative bg-gradient-to-br from-white via-amber-50/30 to-yellow-50/50 dark:from-slate-800 dark:via-amber-900/10 dark:to-slate-900 p-10 rounded-3xl shadow-2xl hover:shadow-3xl transition-all duration-700 transform hover:-translate-y-4 hover:scale-105 border border-slate-200/50 dark:border-slate-700/50 overflow-hidden'>
                  {/* Subtle glow effect */}
                  <div className='absolute inset-0 bg-gradient-to-br from-amber-500/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500'></div>

                  {/* Horizontal Icon and Title */}
                  <div className='relative flex items-center mb-8'>
                    <div className='w-16 h-16 bg-gradient-to-r from-amber-500 to-yellow-500 rounded-2xl flex items-center justify-center mr-6 group-hover:scale-110 transition-transform duration-500 shadow-xl'>
                      <Sparkles className='w-8 h-8 text-white' />
                    </div>
                    <h3 className='text-3xl font-black text-slate-800 dark:text-white tracking-tight'>
                      Our Vision
                    </h3>
                  </div>

                  <p className='relative text-lg text-slate-600 dark:text-gray-300 leading-relaxed font-light'>
                    WOPEDE envisions a world where women lead dignified lives,
                    support their families effectively, and act as ambassadors
                    of peace. We strive to break cycles of poverty and conflict
                    by fostering resilience, growth, and harmony.
                  </p>

                  {/* Decorative accent */}
                  <div className='relative mt-6 w-full h-0.5 bg-gradient-to-r from-amber-500/50 to-transparent group-hover:from-amber-500 transition-all duration-500'></div>
                </div>
              </AnimatedSection>
              {/* Values */}
              <AnimatedSection delay={800}>
                <div className='group relative bg-gradient-to-br from-white via-emerald-50/30 to-green-50/50 dark:from-slate-800 dark:via-emerald-900/10 dark:to-slate-900 p-10 rounded-3xl shadow-2xl hover:shadow-3xl transition-all duration-700 transform hover:-translate-y-4 hover:scale-105 border border-slate-200/50 dark:border-slate-700/50 overflow-hidden'>
                  {/* Subtle glow effect */}
                  <div className='absolute inset-0 bg-gradient-to-br from-emerald-500/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500'></div>

                  {/* Horizontal Icon and Title */}
                  <div className='relative flex items-center mb-4'>
                    <div className='w-16 h-16 bg-gradient-to-r from-emerald-500 to-green-500 rounded-2xl flex items-center justify-center mr-6  shadow-xl'>
                      <Heart className='w-8 h-8 text-white' />
                    </div>
                    <h3 className='text-3xl font-black text-slate-800 dark:text-white tracking-tight'>
                      Our Values
                    </h3>
                  </div>
                  <div className='relative space-y-1'>
                    {values.map((value, index) => (
                      <div
                        key={index}
                        className='flex items-start group/item hover:bg-white/50 dark:hover:bg-slate-700/30  rounded-xl transition-all duration-300'
                      >
                        <div className='w-8 h-8 bg-gradient-to-r from-emerald-500 to-green-500 rounded-full flex items-center justify-center mr-4 flex-shrink-0 group-hover/item:scale-110 transition-transform duration-300 shadow-lg'>
                          {value.icon}
                        </div>
                        <div>
                          <span className='font-bold text-emerald-800 dark:text-emerald-400 text-lg'>
                            {value.title}:
                          </span>
                          <span className='text-slate-600 dark:text-gray-300 ml-2 font-light'>
                            {value.description}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Decorative accent */}
                  <div className='relative mt-6 w-full h-0.5 bg-gradient-to-r from-emerald-500/50 to-transparent group-hover:from-emerald-500 transition-all duration-500'></div>
                </div>
              </AnimatedSection>
            </div>

            {/* Enhanced Call to Action */}
            {/* <AnimatedSection delay={1000}>
              <div className='relative text-center mt-16'>
                <Link
                  to='/contact'
                  className='group relative inline-flex items-center space-x-4 bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 text-white px-12 py-6 rounded-2xl font-bold text-xl hover:from-blue-500 hover:via-blue-600 hover:to-indigo-600 transition-all duration-500 transform hover:scale-110 hover:-translate-y-2 shadow-2xl hover:shadow-blue-500/25 overflow-hidden'
                >
                  <div className='absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500'></div>
                  <span className='relative tracking-wide'>
                    Join Our Mission
                  </span>
                  <div className='relative w-0 group-hover:w-8 h-0.5 bg-white transition-all duration-500'></div>
                  <span className='relative transform group-hover:translate-x-2 transition-transform duration-500 text-2xl'>
                    →
                  </span>
                </Link>
                <p className='mt-6 text-slate-500 dark:text-gray-400 text-sm'>
                  Be part of the change. Transform lives together.
                </p>
              </div>
            </AnimatedSection> */}
          </div>
        </AnimatedSection>
      </div>
    </section>
  );
};

export default React.memo(MissionVisionValues);
