import React from 'react';
import { ArrowRight, CheckCircle } from 'lucide-react';
import AnimatedSection from './AnimatedSection';
import { Link } from 'react-router-dom';
import { programs, trainings } from '@/data/programsData';
import { images } from '@/utils/images';

interface ProgramsSectionProps {
  title?: string;
  subtitle?: string;
  description?: string;
  showHeader?: boolean;
  showTrainings?: boolean;
  showProgramImages?: boolean;
  showTrainingImages?: boolean;
  maxPrograms?: number;
  variant?: 'default' | 'compact' | 'detailed';
  backgroundColor?: string;
  showCTA?: boolean;
}

const ProgramsSection: React.FC<ProgramsSectionProps> = ({
  title = 'Our Programs',
  subtitle = 'Empowering Women Through Education',
  description = 'Comprehensive programs designed to empower women through education, skills development, and community support.',
  showHeader = true,
  showTrainings = true,
  showProgramImages = true,
  showTrainingImages = true,
  maxPrograms,
  variant = 'default',
  backgroundColor = 'bg-white dark:bg-gray-900',
  showCTA = true,
}) => {
  const displayPrograms = maxPrograms
    ? programs.slice(0, maxPrograms)
    : programs;
  const displayTrainings = maxPrograms
    ? trainings.slice(0, maxPrograms)
    : trainings;

  return (
    <section className={`py-20 ${backgroundColor}`}>
      <div className='container mx-auto px-4 lg:px-8 max-w-7xl'>
        {/* Header */}
        {showHeader && (
          <AnimatedSection>
            <div className='text-center mb-16'>
              <h2 className='text-3xl md:text-4xl lg:text-5xl font-bold text-navy-800 dark:text-white mb-6'>
                {title}
              </h2>
              <div className='w-24 h-1 bg-gradient-to-r from-gold-500 to-navy-600 mx-auto mb-6 rounded-full'></div>
              <p className='text-xl text-gray-700 dark:text-gray-300 max-w-4xl mx-auto leading-relaxed'>
                {description}
              </p>
            </div>
          </AnimatedSection>
        )}

        {/* Main Programs */}
        <div className='grid lg:grid-cols-3 gap-8 mb-16'>
          {displayPrograms.map((program, index) => (
            <AnimatedSection key={index} delay={index * 200}>
              <div className='group bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden border border-gray-100 dark:border-gray-700'>
                {/* Program Image - Conditional */}
                {showProgramImages && (
                  <div className='relative h-56 overflow-hidden'>
                    <img
                      src={program.image || images.programs.training.general1}
                      alt={program.title}
                      className='w-full h-full object-cover group-hover:scale-110 transition-transform duration-500'
                    />
                    {/* Subtle gradient for text readability */}
                    <div className='absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent'></div>
                    <div className='absolute top-4 left-4 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-full p-3 shadow-lg'>
                      <span className='text-2xl'>{program.icon}</span>
                    </div>
                  </div>
                )}

                {/* Icon for non-image version */}
                {!showProgramImages && (
                  <div className='p-6 pb-0'>
                    <div className='w-16 h-16 bg-gradient-to-r from-navy-500 to-gold-500 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg'>
                      <span className='text-3xl'>{program.icon}</span>
                    </div>
                  </div>
                )}

                {/* Program Content */}
                <div className='p-6'>
                  <div className='mb-4'>
                    <h3 className='text-xl font-bold text-navy-800 dark:text-white mb-2'>
                      {program.title}
                    </h3>
                    <p className='text-gold-600 dark:text-gold-400 font-semibold text-sm'>
                      {program.subtitle}
                    </p>
                  </div>

                  <p className='text-gray-700 dark:text-gray-300 mb-6 leading-relaxed'>
                    {program.description}
                  </p>

                  {/* Features */}
                  <div className='space-y-2 mb-6'>
                    {program.features.map((feature, featureIndex) => (
                      <div
                        key={featureIndex}
                        className='flex items-center text-sm'
                      >
                        <CheckCircle className='w-4 h-4 text-green-500 mr-2 flex-shrink-0' />
                        <span className='text-gray-700 dark:text-gray-300'>
                          {feature}
                        </span>
                      </div>
                    ))}
                  </div>

                  {/* Learn More Button */}
                  <Link
                    to='/programs'
                    className='group/btn inline-flex items-center text-navy-800 dark:text-navy-400 font-semibold hover:text-gold-600 dark:hover:text-gold-400 transition-colors duration-300'
                  >
                    <span>Learn More</span>
                    <ArrowRight className='w-4 h-4 ml-2 group-hover/btn:translate-x-1 transition-transform duration-300' />
                  </Link>
                </div>
              </div>
            </AnimatedSection>
          ))}
        </div>

        {/* Skills Training Section */}
        {showTrainings && (
          <>
            <AnimatedSection delay={600}>
              <div className='text-center mb-12'>
                <h3 className='text-2xl md:text-3xl font-bold text-navy-800 dark:text-white mb-4'>
                  Skills Training Programs
                </h3>
                <p className='text-lg text-gray-700 dark:text-gray-300 max-w-3xl mx-auto'>
                  Practical skills training that opens doors to economic
                  independence and personal growth.
                </p>
              </div>
            </AnimatedSection>

            <div className='grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12'>
              {displayTrainings.map((training, index) => (
                <AnimatedSection key={index} delay={800 + index * 100}>
                  <div className='group bg-white dark:bg-gray-800 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden border border-gray-100 dark:border-gray-700'>
                    {/* Training Image - Conditional */}
                    {showTrainingImages && (
                      <div className='relative h-48 overflow-hidden'>
                        <img
                          src={training.image}
                          alt={training.name}
                          className='w-full h-full object-cover group-hover:scale-110 transition-transform duration-500'
                        />
                        <div className='absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent'></div>
                        <div className='absolute top-4 left-4 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-full p-2 shadow-lg'>
                          <span className='text-xl'>{training.icon}</span>
                        </div>
                        <div className='absolute bottom-4 left-4'>
                          <span className='bg-sky-500 text-white px-3 py-1 rounded-full text-xs font-semibold'>
                            {training.category}
                          </span>
                        </div>
                      </div>
                    )}

                    {/* Training Content */}
                    <div className='p-6'>
                      {/* Icon for non-image version */}
                      {!showTrainingImages && (
                        <div className='flex items-center mb-4'>
                          <div className='w-12 h-12 bg-gradient-to-r from-sky-500 to-blue-500 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300 shadow-lg'>
                            <span className='text-xl'>{training.icon}</span>
                          </div>
                          <span className='bg-sky-500 text-white px-3 py-1 rounded-full text-xs font-semibold'>
                            {training.category}
                          </span>
                        </div>
                      )}

                      <h4 className='text-xl font-bold text-navy-800 dark:text-white mb-3'>
                        {training.name}
                      </h4>
                      <p className='text-gray-700 dark:text-gray-300 text-sm leading-relaxed mb-4'>
                        {training.description}
                      </p>

                      {/* Training Details */}
                      <div className='flex justify-between items-center text-xs text-gray-600 dark:text-gray-400'>
                        <span className='font-semibold'>
                          Duration: {training.duration}
                        </span>
                        <span className='font-semibold'>
                          Level: {training.level}
                        </span>
                      </div>
                    </div>
                  </div>
                </AnimatedSection>
              ))}
            </div>
          </>
        )}

        {/* Call to Action */}
        {showCTA && (
          <AnimatedSection delay={1200}>
            <div className='text-center'>
              <Link
                to='/programs'
                className='group inline-flex items-center space-x-3 bg-gradient-to-r from-navy-800 to-navy-600 text-white px-10 py-5 rounded-2xl font-semibold text-lg hover:from-navy-700 hover:to-navy-500 transition-all duration-300 transform hover:scale-105 shadow-2xl hover:shadow-3xl'
              >
                <span>Explore All Programs</span>
                <div className='w-0 group-hover:w-6 h-0.5 bg-white transition-all duration-300'></div>
                <ArrowRight className='w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-300' />
              </Link>
            </div>
          </AnimatedSection>
        )}
      </div>
    </section>
  );
};

export default React.memo(ProgramsSection);
