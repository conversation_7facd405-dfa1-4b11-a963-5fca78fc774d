
import React from 'react';
import { Training } from '@/data/programsData';

interface TrainingCardProps {
  training: Training;
  index: number;
}

const TrainingCard: React.FC<TrainingCardProps> = ({ training, index }) => {
  return (
    <div className="group bg-white p-4 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 text-center">
      <div className="text-2xl mb-2 group-hover:scale-110 transition-transform duration-300">
        {training.icon}
      </div>
      <h4 className="text-sm font-semibold text-gray-800 mb-1">
        {training.name}
      </h4>
      <p className="text-xs text-gray-600 leading-tight">
        {training.description}
      </p>
    </div>
  );
};

export default React.memo(TrainingCard);
