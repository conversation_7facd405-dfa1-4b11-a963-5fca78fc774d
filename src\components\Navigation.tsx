import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X, ChevronDown, Heart, Users, Award, ShoppingBag, Camera, TrendingUp, Handshake } from 'lucide-react';
import { ThemeToggle } from './ThemeToggle';
import { cn } from '@/lib/utils';

const Navigation = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { name: 'Home', path: '/', icon: Heart },
    { 
      name: 'About', 
      path: '/about', 
      icon: Users,
      dropdown: [
        { name: 'Our Story', path: '/about#story' },
        { name: 'Mission & Vision', path: '/about#mission' },
        { name: 'Our Team', path: '/about#team' },
        { name: 'Values', path: '/about#values' }
      ]
    },
    { name: 'Programs', path: '/programs', icon: Award },
    { name: 'Products', path: '/products', icon: ShoppingBag },
    { 
      name: 'More', 
      path: '#',
      icon: ChevronDown,
      dropdown: [
        { name: 'Gallery', path: '/gallery', icon: Camera },
        { name: 'Impact', path: '/impact', icon: TrendingUp },
        { name: 'Partners', path: '/partners', icon: Handshake }
      ]
    },
    { name: 'Contact', path: '/contact', icon: Users }
  ];

  const isActive = (path: string) => {
    if (path === '/') return location.pathname === '/';
    return location.pathname.startsWith(path);
  };

  return (
    <nav className={cn(
      "fixed top-0 left-0 right-0 z-50 transition-all duration-300",
      isScrolled 
        ? "bg-white/95 dark:bg-gray-900/95 backdrop-blur-md shadow-lg border-b border-gray-200/20 dark:border-gray-700/20" 
        : "bg-transparent"
    )}>
      <div className="container mx-auto px-6">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3 group">
            <div className="w-10 h-10 bg-gradient-to-br from-navy-800 to-navy-600 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-105">
              <Heart className="w-5 h-5 text-white" />
            </div>
            <div className="hidden sm:block">
              <h1 className="text-xl font-bold text-navy-800 dark:text-white">WOPEDE</h1>
              <p className="text-xs text-gray-600 dark:text-gray-400 -mt-1">Empowering Women</p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-1">
            {navItems.map((item) => (
              <div
                key={item.name}
                className="relative"
                onMouseEnter={() => item.dropdown && setActiveDropdown(item.name)}
                onMouseLeave={() => setActiveDropdown(null)}
              >
                {item.dropdown ? (
                  <button className={cn(
                    "flex items-center space-x-1 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
                    "hover:bg-navy-50 dark:hover:bg-gray-800 hover:text-navy-800 dark:hover:text-white",
                    "text-gray-700 dark:text-gray-300"
                  )}>
                    <span>{item.name}</span>
                    <ChevronDown className="w-4 h-4" />
                  </button>
                ) : (
                  <Link
                    to={item.path}
                    className={cn(
                      "flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
                      isActive(item.path)
                        ? "bg-navy-100 dark:bg-navy-800 text-navy-800 dark:text-navy-200"
                        : "text-gray-700 dark:text-gray-300 hover:bg-navy-50 dark:hover:bg-gray-800 hover:text-navy-800 dark:hover:text-white"
                    )}
                  >
                    {item.icon && <item.icon className="w-4 h-4" />}
                    <span>{item.name}</span>
                  </Link>
                )}

                {/* Dropdown Menu */}
                {item.dropdown && activeDropdown === item.name && (
                  <div className="absolute top-full left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-gray-200 dark:border-gray-700 py-2 z-50">
                    {item.dropdown.map((dropdownItem) => (
                      <Link
                        key={dropdownItem.name}
                        to={dropdownItem.path}
                        className="flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-navy-50 dark:hover:bg-gray-700 hover:text-navy-800 dark:hover:text-white transition-colors duration-200"
                      >
                        {dropdownItem.icon && <dropdownItem.icon className="w-4 h-4" />}
                        <span>{dropdownItem.name}</span>
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Right side actions */}
          <div className="flex items-center space-x-4">
            {/* CTA Button */}
            <Link
              to="/contact"
              className="hidden md:inline-flex items-center space-x-2 bg-gradient-to-r from-amber-600 to-yellow-600 text-white px-6 py-2 rounded-xl font-semibold hover:from-amber-700 hover:to-yellow-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              <Heart className="w-4 h-4" />
              <span>Join Our Mission</span>
            </Link>
            
            <ThemeToggle />
            
            {/* Mobile menu button */}
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="lg:hidden p-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-navy-50 dark:hover:bg-gray-800 transition-colors duration-200"
            >
              {isOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="lg:hidden py-4 border-t border-gray-200 dark:border-gray-700">
            <div className="space-y-2">
              {navItems.map((item) => (
                <div key={item.name}>
                  {item.dropdown ? (
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                        <span>{item.name}</span>
                      </div>
                      <div className="pl-6 space-y-1">
                        {item.dropdown.map((dropdownItem) => (
                          <Link
                            key={dropdownItem.name}
                            to={dropdownItem.path}
                            onClick={() => setIsOpen(false)}
                            className="flex items-center space-x-3 px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-navy-800 dark:hover:text-white transition-colors duration-200"
                          >
                            {dropdownItem.icon && <dropdownItem.icon className="w-4 h-4" />}
                            <span>{dropdownItem.name}</span>
                          </Link>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <Link
                      to={item.path}
                      onClick={() => setIsOpen(false)}
                      className={cn(
                        "flex items-center space-x-3 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
                        isActive(item.path)
                          ? "bg-navy-100 dark:bg-navy-800 text-navy-800 dark:text-navy-200"
                          : "text-gray-700 dark:text-gray-300 hover:bg-navy-50 dark:hover:bg-gray-800"
                      )}
                    >
                      {item.icon && <item.icon className="w-4 h-4" />}
                      <span>{item.name}</span>
                    </Link>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navigation;