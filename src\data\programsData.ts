export interface Program {
  title: string;
  subtitle: string;
  description: string;
  features: string[];
  color: string;
  icon: string;
  image: string;
}

export interface Training {
  name: string;
  icon: string;
  description: string;
  image: string;
  category: string;
  duration?: string;
  level?: string;
}

export const programs: Program[] = [
  {
    title: 'Serenity Alliance',
    subtitle: 'Building Peace Together',
    description:
      'Promoting collaboration among women from diverse backgrounds, fostering unity and understanding through cultural exchange and peacebuilding initiatives.',
    features: [
      'Peace Building Capacity',
      'Cultural Exchange',
      'Community Solidarity',
      'Conflict Resolution',
    ],
    color: 'from-blue-500 to-blue-600',
    icon: '🕊️',
    image: '/assets/stakeholders.jpg',
  },
  {
    title: 'Skills Development',
    subtitle: 'Empowering Through Education',
    description:
      'Equipping women with practical skills to foster economic independence and self-reliance through comprehensive training programs.',
    features: [
      'Hands-on Training',
      'Financial Literacy',
      'Entrepreneurship Support',
      'Certification Programs',
    ],
    color: 'from-gold-500 to-gold-600',
    icon: '🎓',
    image: '/assets/training1.jpg',
  },
  {
    title: 'Community Health',
    subtitle: 'Healing Hearts & Minds',
    description:
      'Providing counseling services and trauma recovery programs for women facing emotional and psychological challenges.',
    features: [
      'Trauma Counseling',
      'SGBV Support',
      'Emotional Healing',
      'Mental Health Awareness',
    ],
    color: 'from-green-500 to-green-600',
    icon: '💚',
    image: '/assets/training2.jpg',
  },
];

export const trainings: Training[] = [
  {
    name: 'Sewing & Tailoring',
    icon: '🧵',
    description: 'Master professional garment making and fashion design skills',
    image: '/assets/sewing.jpg',
    category: 'Fashion & Textiles',
    duration: '6 months',
    level: 'Beginner to Advanced',
  },
  {
    name: 'Hairdressing & Beauty',
    icon: '💄',
    description: 'Professional grooming, styling, and beauty services',
    image: '/assets/hair1.jpg',
    category: 'Beauty & Personal Care',
    duration: '4 months',
    level: 'Beginner to Professional',
  },
  {
    name: 'Catering & Food Services',
    icon: '🍰',
    description: 'Culinary expertise and food business management',
    image: '/assets/bus2.jpg',
    category: 'Food & Hospitality',
    duration: '5 months',
    level: 'All levels',
  },
  {
    name: 'Arts & Crafts',
    icon: '🎨',
    description:
      'Traditional and modern craft techniques for income generation',
    image: '/assets/art1.jpg',
    category: 'Creative Arts',
    duration: '3 months',
    level: 'All levels',
  },
  {
    name: 'Business Development',
    icon: '📈',
    description: 'Entrepreneurship skills and small business management',
    image: '/assets/bus1.jpg',
    category: 'Business & Finance',
    duration: '4 months',
    level: 'Intermediate',
  },
  {
    name: 'Digital & Computer Skills',
    icon: '💻',
    description: 'Modern workplace computer and digital literacy skills',
    image: '/assets/training3.jpg',
    category: 'Technology',
    duration: '3 months',
    level: 'Beginner to Intermediate',
  },
];
