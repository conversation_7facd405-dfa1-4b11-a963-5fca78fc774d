export interface Program {
  title: string;
  subtitle: string;
  description: string;
  features: string[];
  color: string;
  icon: string;
}

export interface Training {
  name: string;
  icon: string;
  description: string;
}

export const programs: Program[] = [
  {
    title: 'Serenity Alliance',
    subtitle: 'Building Peace Together',
    description:
      'Promoting collaboration among women from diverse backgrounds, fostering unity and understanding through cultural exchange and peacebuilding initiatives.',
    features: [
      'Peace Building Capacity',
      'Cultural Exchange',
      'Community Solidarity',
      'Conflict Resolution',
    ],
    color: 'from-blue-500 to-blue-600',
    icon: '🕊️',
    image:
      'https://images.unsplash.com/photo-1559027615-cd4628902d4a?w=400&h=300&fit=crop&crop=center',
  },
  {
    title: 'Skills Development',
    subtitle: 'Empowering Through Education',
    description:
      'Equipping women with practical skills to foster economic independence and self-reliance through comprehensive training programs.',
    features: [
      'Hands-on Training',
      'Financial Literacy',
      'Entrepreneurship Support',
      'Certification Programs',
    ],
    color: 'from-gold-500 to-gold-600',
    icon: '🎓',
    image:
      'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=400&h=300&fit=crop&crop=center',
  },
  {
    title: 'Community Health',
    subtitle: 'Healing Hearts & Minds',
    description:
      'Providing counseling services and trauma recovery programs for women facing emotional and psychological challenges.',
    features: [
      'Trauma Counseling',
      'SGBV Support',
      'Emotional Healing',
      'Mental Health Awareness',
    ],
    color: 'from-green-500 to-green-600',
    icon: '💚',
    image:
      'https://images.unsplash.com/photo-1582213782179-e0d53f98f2ca?w=400&h=300&fit=crop&crop=center',
  },
];

export const trainings: Training[] = [
  {
    name: 'Sewing & Knitting',
    icon: '🧵',
    description: 'Create beautiful clothing and accessories',
  },
  {
    name: 'Hairdressing & Beauty',
    icon: '💄',
    description: 'Professional grooming and styling skills',
  },
  {
    name: 'Catering & Baking',
    icon: '🍰',
    description: 'Culinary expertise for food businesses',
  },
  {
    name: 'Language Skills',
    icon: '🗣️',
    description: 'Communication and integration support',
  },
  {
    name: 'Digital Literacy',
    icon: '💻',
    description: 'Modern workplace computer skills',
  },
  {
    name: 'Financial Literacy',
    icon: '💰',
    description: 'Smart money management and budgeting',
  },
];
