
import React from 'react';
import { Program } from '@/data/programsData';

interface ProgramCardProps {
  program: Program;
  index: number;
}

const ProgramCard: React.FC<ProgramCardProps> = ({ program, index }) => {
  return (
    <div className="group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden">
      <div className={`bg-gradient-to-r ${program.color} p-6 text-white text-center`}>
        <div className="text-4xl mb-3">{program.icon}</div>
        <h3 className="text-xl font-bold mb-1">{program.title}</h3>
        <p className="text-sm opacity-90">{program.subtitle}</p>
      </div>

      <div className="p-6">
        <p className="text-gray-600 text-sm leading-relaxed mb-4">
          {program.description}
        </p>

        <div className="space-y-2">
          {program.features.map((feature, idx) => (
            <div key={idx} className="flex items-center text-sm">
              <div className={`w-2 h-2 bg-gradient-to-r ${program.color} rounded-full mr-3`}></div>
              <span className="text-gray-700">{feature}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default React.memo(ProgramCard);
