import React from 'react';
import AnimatedSection from './AnimatedSection';
import ProgramCard from './ProgramCard';
import TrainingCard from './TrainingCard';
import { programs, trainings } from '@/data/programsData';

const Programs: React.FC = () => {
  return (
    <section
      id='programs'
      className='py-20 bg-gradient-to-br from-gray-50 via-white to-amber-50/30 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative overflow-hidden'
    >
      <div className='container mx-auto px-6 relative z-10'>
        <div className='max-w-6xl mx-auto'>
          {/* Header */}
          <AnimatedSection>
            <div className='text-center mb-3'>
              <h2 className='text-4xl md:text-5xl lg:text-6xl font-bold text-gray-800 dark:text-white mb-6 gradient-text-hero'>
                Our Programs
              </h2>
              <div className='w-24 h-1 bg-gradient-to-r from-amber-500 via-yellow-500 to-amber-600 mx-auto mb-6 rounded-full' />
              <p className='text-elegant text-lg md:text-xl text-gray-700 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed'>
                Three powerful pillars designed to nurture women's potential and
                create lasting change in communities.
              </p>
            </div>
          </AnimatedSection>

          {/* Programs Grid with Images */}
          <AnimatedSection delay={300}>
            <div className='relative mb-20 overflow-hidden rounded-3xl'>
              {/* Enhanced background */}
              <div className='absolute inset-0 bg-gradient-to-br from-white/90 via-amber-50/70 to-navy-50/40 dark:from-gray-800/90 dark:via-gray-700/70 dark:to-gray-800/40 backdrop-blur-sm' />

              {/* Hero Image Section */}
              <div className='relative z-10 p-8'>
                <div className='mb-12'>
                  <div className='aspect-[16/6] rounded-2xl overflow-hidden shadow-2xl bg-gradient-to-r from-navy-800 to-navy-900'>
                    <div className='absolute inset-0 bg-gradient-to-r from-navy-800/80 to-transparent flex items-center justify-center'>
                      <div className='text-center text-white px-8'>
                        <h3 className='text-2xl md:text-3xl font-bold mb-4'>
                          Empowering Women Through Education
                        </h3>
                        <p className='text-lg opacity-90 max-w-2xl'>
                          Building skills, confidence, and sustainable futures
                          for refugee women and their communities
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className='grid md:grid-cols-3 gap-8'>
                  {programs.map((program, index) => (
                    <AnimatedSection key={index} delay={400 + index * 200}>
                      <ProgramCard program={program} index={index} />
                    </AnimatedSection>
                  ))}
                </div>
              </div>
            </div>
          </AnimatedSection>

          {/* Training Programs */}
          <AnimatedSection delay={800}>
            <div className='card-elegant p-10 shadow-2xl'>
              <div className='text-center mb-10'>
                <h3 className='text-3xl md:text-4xl font-bold text-gray-800 dark:text-white mb-4 gradient-text-hero'>
                  Training Programs
                </h3>
                <p className='text-elegant text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto'>
                  Practical skills that transform lives and build sustainable
                  futures for refugee women.
                </p>
              </div>

              <div className='grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-10'>
                {trainings.map((training, index) => (
                  <AnimatedSection key={index} delay={1000 + index * 100}>
                    <TrainingCard training={training} index={index} />
                  </AnimatedSection>
                ))}
              </div>

              <AnimatedSection delay={1600}>
                <div className='text-center'>
                  <a
                    href='#contact'
                    className='gradient-button text-white px-10 py-4 rounded-xl font-bold text-lg hover-elegant shadow-xl inline-block'
                  >
                    Join Our Programs
                  </a>
                </div>
              </AnimatedSection>
            </div>
          </AnimatedSection>
        </div>
      </div>
    </section>
  );
};

export default React.memo(Programs);
