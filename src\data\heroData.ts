
export interface HeroButton {
  text: string;
  target: string;
  variant: 'primary' | 'secondary' | 'outline';
  icon: string;
}

export interface HeroStat {
  number: string;
  title: string;
  description: string;
  delay: number;
}

export const heroConfig = {
  badge: {
    text: "Since 1999 • Transforming Lives in Kenya"
  },
  title: {
    lines: ["Empowering", "Women", "Building Peace"],
    highlightIndex: 1,
    gradient: "from-amber-500 via-yellow-500 to-orange-500"
  },
  subtitle: {
    main: "Transforming lives through skills training, counseling and community support",
    founder: "Founded by <PERSON><PERSON> • Serving since 1999"
  },
  buttons: [
    {
      text: "Our Story",
      target: "about",
      variant: "primary" as const,
      icon: "heart"
    },
    {
      text: "Our Impact", 
      target: "business",
      variant: "secondary" as const,
      icon: "target"
    },
    {
      text: "Join Our Mission",
      target: "contact", 
      variant: "outline" as const,
      icon: "users"
    }
  ] as <PERSON><PERSON><PERSON><PERSON>[],
  stats: [
    {
      number: "25+",
      title: "Years of Service",
      description: "Dedicated to women's empowerment",
      delay: 1800
    },
    {
      number: "1000+", 
      title: "Women Empowered",
      description: "Lives transformed through our programs",
      delay: 2100
    },
    {
      number: "6",
      title: "Training Programs", 
      description: "Comprehensive skill development",
      delay: 2400
    }
  ] as Hero<PERSON><PERSON>[]
};
