import React from 'react';
import {
  Handshake,
  Users,
  Award,
  Globe,
  Target,
  Building,
  Heart,
  ArrowRight,
} from 'lucide-react';
import AnimatedSection from '@/components/AnimatedSection';
import GallerySection from '@/components/GallerySection';
import Footer from '@/components/Footer';

const Partners = () => {
  const partners = [
    {
      name: 'Pamoja Trust',
      description:
        'Supporting community development and empowerment initiatives across Kenya.',
      focus: 'Community Development',
      logo: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=200&h=100&fit=crop&crop=center',
      partnership: 'Strategic Partner',
      since: '2018',
      impact: 'Funded 3 major training programs',
    },
    {
      name: 'Inkomoko',
      description:
        'Providing business training and financial services to entrepreneurs in East Africa.',
      focus: 'Business Training',
      logo: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=200&h=100&fit=crop&crop=center',
      partnership: 'Program Partner',
      since: '2020',
      impact: 'Trained 200+ women entrepreneurs',
    },
    {
      name: 'Cohere',
      description:
        'Fostering social cohesion and integration in refugee and host communities.',
      focus: 'Social Cohesion',
      logo: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=200&h=100&fit=crop&crop=center',
      partnership: 'Implementation Partner',
      since: '2019',
      impact: 'Reached 15 communities',
    },
    {
      name: 'Refugee Point',
      description:
        'Connecting refugees with opportunities and resources for sustainable livelihoods.',
      focus: 'Refugee Support',
      logo: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=200&h=100&fit=crop&crop=center',
      partnership: 'Resource Partner',
      since: '2021',
      impact: 'Connected 500+ refugees to services',
    },
    {
      name: 'JRS (Jesuit Refugee Service)',
      description:
        'Serving, accompanying, and advocating for the rights of refugees and displaced persons.',
      focus: 'Refugee Services',
      logo: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=200&h=100&fit=crop&crop=center',
      partnership: 'Advocacy Partner',
      since: '2017',
      impact: 'Advocated for 1000+ refugees',
    },
    {
      name: 'UN Women Kenya',
      description:
        "Promoting gender equality and women's empowerment across Kenya.",
      focus: 'Gender Equality',
      logo: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=200&h=100&fit=crop&crop=center',
      partnership: 'Technical Partner',
      since: '2020',
      impact: 'Policy advocacy and capacity building',
    },
  ];

  const partnershipTypes = [
    {
      title: 'Strategic Partnerships',
      description:
        'Long-term collaborations that shape our programs and expand our reach.',
      icon: Target,
      color: 'from-blue-500 to-blue-600',
      benefits: [
        'Joint program development',
        'Shared resources',
        'Policy influence',
        'Capacity building',
      ],
    },
    {
      title: 'Implementation Partners',
      description:
        'Organizations that help us deliver programs directly to communities.',
      icon: Users,
      color: 'from-green-500 to-green-600',
      benefits: [
        'Program delivery',
        'Community access',
        'Local expertise',
        'Cultural sensitivity',
      ],
    },
    {
      title: 'Funding Partners',
      description:
        'Donors and foundations that provide financial support for our initiatives.',
      icon: Heart,
      color: 'from-red-500 to-red-600',
      benefits: [
        'Financial sustainability',
        'Program expansion',
        'Innovation funding',
        'Emergency support',
      ],
    },
    {
      title: 'Technical Partners',
      description:
        'Experts who provide specialized knowledge and technical assistance.',
      icon: Award,
      color: 'from-purple-500 to-purple-600',
      benefits: [
        'Expert knowledge',
        'Best practices',
        'Quality assurance',
        'Innovation',
      ],
    },
  ];

  const partnershipOpportunities = [
    {
      title: 'Corporate Partnership',
      description:
        'Partner with us to create meaningful CSR programs that empower women.',
      icon: Building,
      benefits: [
        'Brand visibility',
        'Employee engagement',
        'Social impact',
        'Tax benefits',
      ],
      commitment: 'Flexible arrangements from $5,000 annually',
    },
    {
      title: 'Foundation Partnership',
      description:
        "Support specific programs or initiatives aligned with your foundation's mission.",
      icon: Globe,
      benefits: [
        'Targeted impact',
        'Detailed reporting',
        'Site visits',
        'Story sharing',
      ],
      commitment: 'Program-specific funding from $10,000',
    },
    {
      title: 'Individual Sponsorship',
      description: 'Sponsor individual women through their training journey.',
      icon: Heart,
      benefits: [
        'Personal connection',
        'Progress updates',
        'Direct impact',
        'Recognition',
      ],
      commitment: 'From $500 per woman per program',
    },
    {
      title: 'In-Kind Partnership',
      description:
        'Provide goods, services, or expertise to support our operations.',
      icon: Handshake,
      benefits: [
        'Resource efficiency',
        'Skill sharing',
        'Network expansion',
        'Mutual learning',
      ],
      commitment: 'Based on available resources',
    },
  ];

  return (
    <div className='min-h-screen pt-20'>
      {/* Hero Section */}
      <section className='py-20 bg-gradient-to-br from-navy-50 via-white to-gold-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900'>
        <div className='container mx-auto px-6'>
          <AnimatedSection>
            <div className='text-center mb-16'>
              <h1 className='text-4xl md:text-5xl lg:text-6xl font-bold text-navy-800 dark:text-white mb-6'>
                Our{' '}
                <span className='bg-gradient-to-r from-gold-500 to-navy-600 bg-clip-text text-transparent'>
                  Partners
                </span>
              </h1>
              <p className='text-xl text-gray-700 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed'>
                Building stronger communities through strategic partnerships and
                collaborative efforts that amplify our impact.
              </p>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Current Partners */}
      <section className='py-20 bg-white dark:bg-gray-900'>
        <div className='container mx-auto px-6'>
          <AnimatedSection>
            <div className='text-center mb-16'>
              <h2 className='text-3xl md:text-4xl font-bold text-navy-800 dark:text-white mb-6'>
                Our Valued Partners
              </h2>
              <p className='text-lg text-gray-700 dark:text-gray-300 max-w-2xl mx-auto'>
                Organizations that share our vision and work alongside us to
                create lasting change.
              </p>
            </div>
          </AnimatedSection>

          <div className='grid md:grid-cols-2 lg:grid-cols-3 gap-8'>
            {partners.map((partner, index) => (
              <AnimatedSection key={index} delay={index * 200}>
                <div className='bg-gradient-to-br from-navy-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 p-6 rounded-2xl hover:shadow-2xl transition-all duration-300 group hover:scale-105'>
                  <div className='flex items-center space-x-4 mb-4'>
                    <div className='w-16 h-16 bg-white dark:bg-gray-600 rounded-xl overflow-hidden shadow-lg'>
                      <img
                        src={partner.logo}
                        alt={`${partner.name} logo`}
                        className='w-full h-full object-cover'
                      />
                    </div>
                    <div>
                      <h3 className='text-lg font-bold text-navy-800 dark:text-white'>
                        {partner.name}
                      </h3>
                      <div className='flex items-center space-x-2'>
                        <span className='text-xs bg-gold-100 dark:bg-gold-900 text-gold-800 dark:text-gold-200 px-2 py-1 rounded-full'>
                          {partner.partnership}
                        </span>
                        <span className='text-xs text-gray-600 dark:text-gray-400'>
                          Since {partner.since}
                        </span>
                      </div>
                    </div>
                  </div>

                  <p className='text-gray-700 dark:text-gray-300 text-sm mb-4 leading-relaxed'>
                    {partner.description}
                  </p>

                  <div className='space-y-2 mb-4'>
                    <div className='flex items-center justify-between text-sm'>
                      <span className='text-gray-600 dark:text-gray-400'>
                        Focus Area:
                      </span>
                      <span className='text-navy-800 dark:text-white font-semibold'>
                        {partner.focus}
                      </span>
                    </div>
                    <div className='text-sm'>
                      <span className='text-gray-600 dark:text-gray-400'>
                        Impact:{' '}
                      </span>
                      <span className='text-navy-800 dark:text-white'>
                        {partner.impact}
                      </span>
                    </div>
                  </div>

                  {/* <button className='w-full bg-gray-400 text-white py-2 rounded-lg font-semibold hover:from-navy-700 hover:to-gold-700 transition-all duration-300 flex items-center justify-center space-x-2 group'>
                    <span>Learn More</span>
                    <ArrowRight className='w-4 h-4 group-hover:translate-x-1 transition-transform duration-300' />
                  </button> */}
                </div>
              </AnimatedSection>
            ))}
          </div>
        </div>
      </section>

      {/* Partnership Types */}
      <section className='py-20 bg-gradient-to-br from-gold-50 to-navy-50 dark:from-gray-800 dark:to-gray-900'>
        <div className='container mx-auto px-6'>
          <AnimatedSection>
            <div className='text-center mb-16'>
              <h2 className='text-3xl md:text-4xl font-bold text-navy-800 dark:text-white mb-6'>
                Types of Partnerships
              </h2>
              <p className='text-lg text-gray-700 dark:text-gray-300 max-w-2xl mx-auto'>
                We work with different types of partners to maximize our impact
                and reach.
              </p>
            </div>
          </AnimatedSection>

          <div className='grid md:grid-cols-2 gap-8'>
            {partnershipTypes.map((type, index) => (
              <AnimatedSection key={index} delay={index * 300}>
                <div className='bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 group hover:scale-105'>
                  <div className='flex items-center space-x-4 mb-6'>
                    <div
                      className={`w-12 h-12 bg-gradient-to-r ${type.color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}
                    >
                      <type.icon className='w-6 h-6 text-white' />
                    </div>
                    <h3 className='text-xl font-bold text-navy-800 dark:text-white'>
                      {type.title}
                    </h3>
                  </div>

                  <p className='text-gray-700 dark:text-gray-300 mb-6 leading-relaxed'>
                    {type.description}
                  </p>

                  <div className='space-y-2'>
                    <h4 className='font-semibold text-navy-800 dark:text-white mb-3'>
                      Key Benefits:
                    </h4>
                    {type.benefits.map((benefit, idx) => (
                      <div key={idx} className='flex items-center space-x-2'>
                        <div
                          className={`w-2 h-2 bg-gradient-to-r ${type.color} rounded-full`}
                        ></div>
                        <span className='text-gray-700 dark:text-gray-300 text-sm'>
                          {benefit}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </AnimatedSection>
            ))}
          </div>
        </div>
      </section>

      {/* Partnership Opportunities */}
      <section className='py-20 bg-white dark:bg-gray-900'>
        <div className='container mx-auto px-6'>
          <AnimatedSection>
            <div className='text-center mb-16'>
              <h2 className='text-3xl md:text-4xl font-bold text-navy-800 dark:text-white mb-6'>
                Partnership Opportunities
              </h2>
              <p className='text-lg text-gray-700 dark:text-gray-300 max-w-2xl mx-auto'>
                Join us in creating meaningful change. We offer flexible
                partnership options to match your goals and capacity.
              </p>
            </div>
          </AnimatedSection>

          <div className='grid md:grid-cols-2 gap-8'>
            {partnershipOpportunities.map((opportunity, index) => (
              <AnimatedSection key={index} delay={index * 200}>
                <div className='bg-gradient-to-br from-navy-50 to-gold-50 dark:from-gray-800 dark:to-gray-700 p-6 rounded-2xl hover:shadow-xl transition-all duration-300 group hover:scale-105'>
                  <div className='flex items-center space-x-3 mb-4'>
                    <div className='w-10 h-10 bg-gradient-to-r from-navy-600 to-blue-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300'>
                      <opportunity.icon className='w-5 h-5 text-white' />
                    </div>
                    <h3 className='text-xl font-bold text-navy-800 dark:text-white'>
                      {opportunity.title}
                    </h3>
                  </div>

                  <p className='text-gray-700 dark:text-gray-300 mb-4 leading-relaxed'>
                    {opportunity.description}
                  </p>

                  <div className='space-y-3 mb-4'>
                    <div>
                      <h4 className='font-semibold text-navy-800 dark:text-white mb-2'>
                        Benefits:
                      </h4>
                      <div className='grid grid-cols-2 gap-2'>
                        {opportunity.benefits.map((benefit, idx) => (
                          <div
                            key={idx}
                            className='flex items-center space-x-2'
                          >
                            <div className='w-1.5 h-1.5 bg-gold-500 rounded-full'></div>
                            <span className='text-gray-700 dark:text-gray-300 text-xs'>
                              {benefit}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className='bg-white dark:bg-gray-800 p-3 rounded-lg'>
                      <span className='text-xs text-gray-600 dark:text-gray-400'>
                        Commitment:{' '}
                      </span>
                      <span className='text-navy-800 dark:text-white font-semibold text-sm'>
                        {opportunity.commitment}
                      </span>
                    </div>
                  </div>

                  <button className='w-full bg-gradient-to-r from-navy-600 to-blue-600 text-white py-3 rounded-xl font-semibold hover:from-navy-700 hover:to-gold-700 transition-all duration-300 flex items-center justify-center space-x-2 group'>
                    <span>Get Started</span>
                    <ArrowRight className='w-4 h-4 group-hover:translate-x-1 transition-transform duration-300' />
                  </button>
                </div>
              </AnimatedSection>
            ))}
          </div>
        </div>
      </section>
      <GallerySection />
      {/* Call to Action */}
      <section className='py-20 bg-gradient-to-r from-navy-800 to-navy-600 text-white'>
        <div className='container mx-auto px-6'>
          <AnimatedSection>
            <div className='text-center'>
              <h2 className='text-3xl md:text-4xl font-bold mb-6'>
                Ready to Partner With Us?
              </h2>
              <p className='text-xl mb-8 max-w-2xl mx-auto opacity-90'>
                Let's work together to create lasting change in women's lives
                and build stronger, more peaceful communities.
              </p>
              <div className='flex flex-col sm:flex-row gap-4 justify-center'>
                <button className='bg-gradient-to-r from-gold-500 to-gold-400 text-navy-800 px-8 py-4 rounded-xl font-bold text-lg hover:from-gold-400 hover:to-gold-300 transition-all duration-300 transform hover:scale-105'>
                  Start a Partnership
                </button>
                <button className='border-2 border-white text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-white hover:text-navy-800 transition-all duration-300'>
                  Download Partnership Guide
                </button>
              </div>
            </div>
          </AnimatedSection>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Partners;
