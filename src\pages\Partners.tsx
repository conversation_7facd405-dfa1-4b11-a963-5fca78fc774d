import React from 'react';
import PageHero from '@/components/PageHero';
import PartnersSection from '@/components/PartnersSection';
import GallerySection from '@/components/GallerySection';
import Footer from '@/components/Footer';
import { images } from '@/utils/images';

const Partners = () => {
  return (
    <div className='min-h-screen pt-20'>
      {/* Hero Section */}
      <PageHero
        title='Partnership & Collaboration'
        subtitle='Building Stronger Communities Together'
        description='We collaborate with leading organizations to maximize our impact and create lasting change in communities across Kenya.'
        backgroundImage={images.community.events.gathering}
        height='sm'
        overlay='navy'
        scrollToId='partners-content'
      />

      {/* Partners Content */}
      <div id='partners-content'>
        {/* Featured Partners */}
        <PartnersSection
          title='Our Key Partners'
          variant='featured'
          showHeader={true}
          backgroundColor='bg-white dark:bg-gray-900'
        />

        {/* Partner Categories */}
        <PartnersSection
          title='Partnership Categories'
          variant='categories'
          showHeader={true}
          showCTA={false}
          backgroundColor='bg-gradient-to-br from-navy-50 via-white to-gold-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900'
        />

        {/* All Partners Logo Grid */}
        <PartnersSection
          title='All Our Partners'
          variant='logos'
          showHeader={true}
          showCTA={false}
          backgroundColor='bg-white dark:bg-gray-900'
        />
      </div>

      <GallerySection />
      <Footer />
    </div>
  );
};

export default Partners;
