import React from 'react';
import Hero from '@/components/Hero';
import MissionVisionValues from '@/components/MissionVisionValues';
import ProgramsSection from '@/components/ProgramsSection';
import PartnersSection from '@/components/PartnersSection';
import Business from '@/components/Business';

import SuccessStories from '@/components/SuccessStories';
import GallerySection from '@/components/GallerySection';
import Newsletter from '@/components/Newsletter';
import Footer from '@/components/Footer';

const Home = () => {
  return (
    <div className='min-h-screen'>
      <Hero />
      <MissionVisionValues />
      <ProgramsSection
        maxPrograms={3}
        showTrainings={true}
        backgroundColor='bg-gradient-to-br from-white via-sky-50/30 to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900'
      />
      <PartnersSection
        variant='featured'
        maxPartners={4}
        backgroundColor='bg-gradient-to-br from-navy-50 via-white to-gold-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900'
      />
      <Business />
      <SuccessStories />
      <GallerySection />
      <Newsletter />
      <Footer />
    </div>
  );
};

export default Home;
