import React, { useState } from 'react';
import {
  MapPin,
  Phone,
  Mail,
  Clock,
  Send,
  MessageCircle,
  Users,
  Calendar,
} from 'lucide-react';
import AnimatedSection from '@/components/AnimatedSection';
import GallerySection from '@/components/GallerySection';
import Footer from '@/components/Footer';

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
    inquiryType: 'general',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    console.log('Form submitted:', formData);
  };

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const contactInfo = [
    {
      icon: MapPin,
      title: 'Our Location',
      details: ['Kakuma Refugee Camp', 'Turkana County, Kenya'],
      color: 'from-blue-500 to-blue-600',
    },
    {
      icon: Phone,
      title: 'Phone Numbers',
      details: ['+254 712 345 678', '+254 734 567 890'],
      color: 'from-green-500 to-green-600',
    },
    {
      icon: Mail,
      title: 'Email Address',
      details: ['<EMAIL>', '<EMAIL>'],
      color: 'from-purple-500 to-purple-600',
    },
    {
      icon: Clock,
      title: 'Office Hours',
      details: [
        'Monday - Friday: 8:00 AM - 5:00 PM',
        'Saturday: 9:00 AM - 2:00 PM',
      ],
      color: 'from-orange-500 to-orange-600',
    },
  ];

  const inquiryTypes = [
    { value: 'general', label: 'General Inquiry' },
    { value: 'programs', label: 'Program Information' },
    { value: 'volunteer', label: 'Volunteer Opportunities' },
    { value: 'partnership', label: 'Partnership' },
    { value: 'donation', label: 'Donations' },
    { value: 'products', label: 'Product Orders' },
  ];

  return (
    <div className='min-h-screen pt-20'>
      {/* Hero Section */}
      <section className='py-20 bg-gradient-to-br from-navy-50 via-white to-gold-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900'>
        <div className='container mx-auto px-6'>
          <AnimatedSection>
            <div className='text-center mb-16'>
              <h1 className='text-4xl md:text-5xl lg:text-6xl font-bold text-navy-800 dark:text-white mb-6'>
                Contact{' '}
                <span className='bg-gradient-to-r from-gold-500 to-navy-600 bg-clip-text text-transparent'>
                  Us
                </span>
              </h1>
              <p className='text-xl text-gray-700 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed'>
                Get in touch with us to learn more about our programs, volunteer
                opportunities, or how you can support our mission.
              </p>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Contact Information */}
      <section className='py-20 bg-white dark:bg-gray-900'>
        <div className='container mx-auto px-6'>
          <AnimatedSection>
            <div className='text-center mb-16'>
              <h2 className='text-3xl md:text-4xl font-bold text-navy-800 dark:text-white mb-6'>
                Get In Touch
              </h2>
              <p className='text-lg text-gray-700 dark:text-gray-300 max-w-2xl mx-auto'>
                We're here to answer your questions and help you get involved in
                our mission.
              </p>
            </div>
          </AnimatedSection>

          <div className='grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16'>
            {contactInfo.map((info, index) => (
              <AnimatedSection key={index} delay={index * 200}>
                <div className='bg-gradient-to-br from-navy-50 to-gold-50 dark:from-gray-800 dark:to-gray-700 p-6 rounded-2xl hover:shadow-xl transition-all duration-300 group hover:scale-105'>
                  <div
                    className={`w-12 h-12 bg-gradient-to-r ${info.color} rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <info.icon className='w-6 h-6 text-white' />
                  </div>
                  <h3 className='text-lg font-bold text-navy-800 dark:text-white mb-3'>
                    {info.title}
                  </h3>
                  <div className='space-y-1'>
                    {info.details.map((detail, idx) => (
                      <p
                        key={idx}
                        className='text-gray-700 dark:text-gray-300 text-sm'
                      >
                        {detail}
                      </p>
                    ))}
                  </div>
                </div>
              </AnimatedSection>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form and Map */}
      <section className='py-20 bg-gradient-to-br from-gold-50 to-navy-50 dark:from-gray-800 dark:to-gray-900'>
        <div className='container mx-auto px-6'>
          <div className='grid lg:grid-cols-2 gap-12'>
            {/* Contact Form */}
            <AnimatedSection>
              <div className='bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-xl'>
                <div className='flex items-center space-x-3 mb-6'>
                  <MessageCircle className='w-6 h-6 text-gold-600 dark:text-gold-400' />
                  <h3 className='text-2xl font-bold text-navy-800 dark:text-white'>
                    Send us a Message
                  </h3>
                </div>

                <form onSubmit={handleSubmit} className='space-y-6'>
                  <div className='grid md:grid-cols-2 gap-6'>
                    <div>
                      <label
                        htmlFor='name'
                        className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'
                      >
                        Full Name *
                      </label>
                      <input
                        type='text'
                        id='name'
                        name='name'
                        required
                        value={formData.name}
                        onChange={handleChange}
                        className='w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-gold-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white'
                        placeholder='Your full name'
                      />
                    </div>
                    <div>
                      <label
                        htmlFor='email'
                        className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'
                      >
                        Email Address *
                      </label>
                      <input
                        type='email'
                        id='email'
                        name='email'
                        required
                        value={formData.email}
                        onChange={handleChange}
                        className='w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-gold-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white'
                        placeholder='<EMAIL>'
                      />
                    </div>
                  </div>

                  <div className='grid md:grid-cols-2 gap-6'>
                    <div>
                      <label
                        htmlFor='phone'
                        className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'
                      >
                        Phone Number
                      </label>
                      <input
                        type='tel'
                        id='phone'
                        name='phone'
                        value={formData.phone}
                        onChange={handleChange}
                        className='w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-gold-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white'
                        placeholder='+254 712 345 678'
                      />
                    </div>
                    <div>
                      <label
                        htmlFor='inquiryType'
                        className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'
                      >
                        Inquiry Type
                      </label>
                      <select
                        id='inquiryType'
                        name='inquiryType'
                        value={formData.inquiryType}
                        onChange={handleChange}
                        className='w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-gold-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white'
                      >
                        {inquiryTypes.map((type) => (
                          <option key={type.value} value={type.value}>
                            {type.label}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div>
                    <label
                      htmlFor='subject'
                      className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'
                    >
                      Subject *
                    </label>
                    <input
                      type='text'
                      id='subject'
                      name='subject'
                      required
                      value={formData.subject}
                      onChange={handleChange}
                      className='w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-gold-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white'
                      placeholder='Brief subject of your message'
                    />
                  </div>

                  <div>
                    <label
                      htmlFor='message'
                      className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'
                    >
                      Message *
                    </label>
                    <textarea
                      id='message'
                      name='message'
                      required
                      rows={6}
                      value={formData.message}
                      onChange={handleChange}
                      className='w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-gold-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none'
                      placeholder='Tell us how we can help you...'
                    />
                  </div>

                  <button
                    type='submit'
                    className='w-full bg-gradient-to-r from-navy-600 to-gold-600 text-white py-4 rounded-xl font-semibold hover:from-navy-700 hover:to-gold-700 transition-all duration-300 flex items-center justify-center space-x-2 group'
                  >
                    <Send className='w-5 h-5 group-hover:animate-pulse' />
                    <span>Send Message</span>
                  </button>
                </form>
              </div>
            </AnimatedSection>

            {/* Map and Additional Info */}
            <AnimatedSection delay={300}>
              <div className='space-y-8'>
                {/* Map Placeholder */}
                <div className='bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-xl'>
                  <h3 className='text-xl font-bold text-navy-800 dark:text-white mb-4 flex items-center'>
                    <MapPin className='w-5 h-5 text-gold-600 dark:text-gold-400 mr-2' />
                    Find Us
                  </h3>
                  <div className='aspect-video bg-gradient-to-br from-navy-100 to-gold-100 dark:from-gray-700 dark:to-gray-600 rounded-xl flex items-center justify-center'>
                    <div className='text-center'>
                      <MapPin className='w-12 h-12 text-navy-600 dark:text-gold-400 mx-auto mb-2' />
                      <p className='text-navy-800 dark:text-white font-semibold'>
                        Kakuma Refugee Camp
                      </p>
                      <p className='text-gray-600 dark:text-gray-400 text-sm'>
                        Turkana County, Kenya
                      </p>
                    </div>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className='bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-xl'>
                  <h3 className='text-xl font-bold text-navy-800 dark:text-white mb-4'>
                    Quick Actions
                  </h3>
                  <div className='space-y-3'>
                    <button className='w-full flex items-center space-x-3 p-3 bg-gradient-to-r from-navy-50 to-gold-50 dark:from-gray-700 dark:to-gray-600 rounded-xl hover:shadow-md transition-all duration-300 group'>
                      <Users className='w-5 h-5 text-navy-600 dark:text-gold-400 group-hover:scale-110 transition-transform duration-300' />
                      <span className='text-navy-800 dark:text-white font-medium'>
                        Volunteer with Us
                      </span>
                    </button>
                    <button className='w-full flex items-center space-x-3 p-3 bg-gradient-to-r from-navy-50 to-gold-50 dark:from-gray-700 dark:to-gray-600 rounded-xl hover:shadow-md transition-all duration-300 group'>
                      <Calendar className='w-5 h-5 text-navy-600 dark:text-gold-400 group-hover:scale-110 transition-transform duration-300' />
                      <span className='text-navy-800 dark:text-white font-medium'>
                        Schedule a Visit
                      </span>
                    </button>
                    <button className='w-full flex items-center space-x-3 p-3 bg-gradient-to-r from-navy-50 to-gold-50 dark:from-gray-700 dark:to-gray-600 rounded-xl hover:shadow-md transition-all duration-300 group'>
                      <MessageCircle className='w-5 h-5 text-navy-600 dark:text-gold-400 group-hover:scale-110 transition-transform duration-300' />
                      <span className='text-navy-800 dark:text-white font-medium'>
                        Join Our Newsletter
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            </AnimatedSection>
          </div>
        </div>
      </section>

      <GallerySection />
      <Footer />
    </div>
  );
};

export default Contact;
