import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Sparkles } from 'lucide-react';

const GallerySection = () => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const items = [
    {
      id: 1,
      title: 'Skills Training Workshop',
      description: 'Women learning valuable skills for economic empowerment',
      image: '/assets/bus1.jpg'
    },
    {
      id: 2,
      title: 'Community Gathering',
      description: 'Building bonds and sharing experiences together',
      image: '/assets/bus2.jpg'
    },
    {
      id: 3,
      title: 'Handcraft Creation',
      description: 'Beautiful products made with traditional techniques',
      image: '/assets/bus3.jpg'
    },
    {
      id: 4,
      title: 'Peace Building Session',
      description: 'Fostering unity and understanding in communities',
      image: '/assets/bus4.jpg'
    },
    {
      id: 5,
      title: 'Training Program',
      description: 'Empowering women through education and skills',
      image: '/assets/training1.jpg'
    },
    {
      id: 6,
      title: 'Community Support',
      description: 'Women supporting each other in their journey',
      image: '/assets/training2.jpg'
    },
    {
      id: 7,
      title: 'Artisan Work',
      description: 'Creating beautiful handcrafted products',
      image: '/assets/art1.jpg'
    },
    {
      id: 8,
      title: 'Leadership Development',
      description: 'Building confident leaders in our community',
      image: '/assets/founder1.jpg'
    }
  ];

  // Auto-scroll effect
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % items.length);
    }, 4000);
    return () => clearInterval(interval);
  }, [items.length]);

  const goToNext = () => {
    setCurrentIndex((prev) => (prev + 1) % items.length);
  };

  const goToPrevious = () => {
    setCurrentIndex((prev) => (prev - 1 + items.length) % items.length);
  };

  return (
    <section
      id='gallery'
      className='py-20 bg-gradient-to-br from-white via-amber-50/30 to-white dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative overflow-hidden'
    >
      <div className='container mx-auto px-6'>
        <div className='w-full'>
          <div className='flex flex-col sm:flex-row items-center justify-between mb-8'>
            <h2 className='text-3xl font-bold text-gray-800 dark:text-white mb-2 sm:mb-0'>
              Our Gallery
            </h2>
            
            <div className='inline-flex items-center space-x-3 bg-white/80 dark:bg-gray-800/80 backdrop-blur-md px-6 py-2 rounded-full border border-amber-200 dark:border-amber-700/30 shadow-lg'>
              <Sparkles className='w-4 h-4 text-amber-500 dark:text-amber-400' />
              <span className='text-gray-800 dark:text-amber-300 font-semibold text-sm tracking-wide'>
                Stories in Every Frame
              </span>
            </div>
          </div>

          {/* Main Featured Image - Centered */}
          <div className='relative mb-5 group flex justify-center'>
            <div className='relative h-70 w-full max-w-2xl lg:h-[300px] rounded-3xl overflow-hidden shadow-2xl'>
              <img
                src={items[currentIndex].image}
                alt={items[currentIndex].title}
                className='w-full h-full object-cover transition-transform duration-700 group-hover:scale-105'
              />
              <div className='absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent'></div>

              {/* Content Overlay */}
              <div className='absolute bottom-0 left-0 right-0 p-6 text-white'>
                <h3 className='text-xl font-bold mb-2'>
                  {items[currentIndex].title}
                </h3>
                {items[currentIndex].description && (
                  <p className='text-sm opacity-90'>
                    {items[currentIndex].description}
                  </p>
                )}
              </div>

              {/* Navigation Arrows */}
              <button
                onClick={goToPrevious}
                className='absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-full p-3 transition-all duration-200 opacity-0 group-hover:opacity-100 shadow-xl border border-white/30'
              >
                <ChevronLeft className='h-5 w-5 text-white' />
              </button>
              <button
                onClick={goToNext}
                className='absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-full p-3 transition-all duration-200 opacity-0 group-hover:opacity-100 shadow-xl border border-white/30'
              >
                <ChevronRight className='h-5 w-5 text-white' />
              </button>

              {/* Slide Indicator */}
              <div className='absolute bottom-4 right-4 bg-black/50 backdrop-blur-sm rounded-full px-3 py-1'>
                <span className='text-white text-sm font-medium'>
                  {currentIndex + 1} / {items.length}
                </span>
              </div>
            </div>
          </div>

          {/* Thumbnail Grid - Extra small thumbnails */}
          <div className='flex justify-center mb-6'>
            <div className='flex gap-2 p-1 bg-white/20 dark:bg-gray-900/30 backdrop-blur-sm rounded-2xl border border-amber-400/20 shadow-lg'>
              {items.map((item, index) => (
                <button
                  key={item.id}
                  onClick={() => setCurrentIndex(index)}
                  className={`w-12 h-12 rounded-lg overflow-hidden transition-all duration-300 ${
                    index === currentIndex
                      ? 'ring-2 ring-amber-500 scale-110 shadow-lg'
                      : 'hover:scale-105 opacity-70 hover:opacity-100'
                  }`}
                >
                  <img
                    src={item.image}
                    alt={item.title}
                    className='w-full h-full object-cover'
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Dots Indicator */}
          <div className='flex justify-center space-x-2 mt-6'>
            {items.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? 'bg-gradient-to-r from-amber-500 to-amber-600 scale-125'
                    : 'bg-gray-300 dark:bg-gray-600 hover:bg-amber-300'
                }`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default GallerySection;