import React from 'react';
import { Heart, Users, Target, Award, Globe, Lightbulb } from 'lucide-react';
import AnimatedSection from '@/components/AnimatedSection';
import PageHero from '@/components/PageHero';
import GallerySection from '@/components/GallerySection';
import Footer from '@/components/Footer';
import { images } from '@/utils/images';

const About = () => {
  const values = [
    {
      icon: Heart,
      title: 'Compassion',
      description:
        'We approach every woman with empathy, understanding their unique challenges and providing support with genuine care.',
    },
    {
      icon: Users,
      title: 'Community',
      description:
        'Building strong networks of support where women can connect, learn from each other, and grow together.',
    },
    {
      icon: Target,
      title: 'Empowerment',
      description:
        'Providing tools, skills, and opportunities that enable women to become self-reliant and confident leaders.',
    },
    {
      icon: Award,
      title: 'Excellence',
      description:
        'Maintaining high standards in all our programs and services to ensure maximum impact and lasting change.',
    },
    {
      icon: Globe,
      title: 'Inclusivity',
      description:
        'Welcoming women from all backgrounds, cultures, and circumstances without discrimination or judgment.',
    },
    {
      icon: Lightbulb,
      title: 'Innovation',
      description:
        'Continuously evolving our approaches and methods to meet the changing needs of the communities we serve.',
    },
  ];

  const team = [
    {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      role: 'Founder & Executive Director',
      image:
        'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=center',
      bio: 'With over 25 years of experience in community development, Modeste founded WOPEDE with a vision to empower refugee women and build lasting peace.',
    },
    {
      name: 'Sarah Mukamana',
      role: 'Program Director',
      image:
        'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=300&h=300&fit=crop&crop=center',
      bio: "Sarah oversees all training programs and has helped design curricula that have transformed hundreds of women's lives.",
    },
    {
      name: 'Grace Wanjiku',
      role: 'Community Outreach Coordinator',
      image:
        'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=300&h=300&fit=crop&crop=center',
      bio: 'Grace builds bridges between communities and ensures our programs reach those who need them most.',
    },
  ];

  return (
    <div className='min-h-screen pt-10'>
      {/* Hero Section with Background Image */}
      <PageHero
        title='About WOPEDE'
        subtitle='Our Story'
        description="For over 25 years, we've been dedicated to empowering women, building peace, and creating sustainable change in communities across Kenya."
        backgroundImage={images.community.events.gathering}
        height='lg'
        overlay='navy'
        scrollToId='story'
      />

      {/* Our Story Section */}
      <section id='story' className='py-20 bg-white dark:bg-gray-900'>
        <div className='container mx-auto px-6'>
          <div className='grid lg:grid-cols-2 gap-12 items-center'>
            <AnimatedSection>
              <div>
                <h2 className='text-3xl md:text-4xl font-bold text-navy-800 dark:text-white mb-6'>
                  Our Story
                </h2>
                <div className='space-y-6 text-gray-700 dark:text-gray-300 leading-relaxed'>
                  <p>
                    WOPEDE (Women's Organization for Peace and Development) was
                    founded in 1999 by Mme Cimpaye Modeste, a visionary leader
                    who recognized the urgent need to support refugee women and
                    promote peace in communities affected by conflict.
                  </p>
                  <p>
                    What started as a small initiative to help a handful of
                    women has grown into a comprehensive organization that has
                    transformed the lives of over 1,000 women through skills
                    training, counseling, and community support programs.
                  </p>
                  <p>
                    Our journey has been one of resilience, hope, and unwavering
                    commitment to the belief that when women are empowered,
                    entire communities thrive. Today, we continue to expand our
                    reach and impact, always staying true to our founding
                    principles of compassion, empowerment, and peace-building.
                  </p>
                </div>
              </div>
            </AnimatedSection>

            <AnimatedSection delay={300}>
              <div className='relative'>
                <img
                  src='https://images.unsplash.com/photo-1559027615-cd4628902d4a?w=600&h=400&fit=crop&crop=center'
                  alt='WOPEDE community gathering'
                  className='rounded-2xl shadow-2xl'
                />
                <div className='absolute -bottom-6 -right-6 bg-gradient-to-r from-gold-500 to-gold-400 text-white p-6 rounded-2xl shadow-xl'>
                  <div className='text-2xl font-bold'>1999</div>
                  <div className='text-sm'>Founded</div>
                </div>
              </div>
            </AnimatedSection>
          </div>
        </div>
      </section>

      {/* Mission & Vision Section */}
      <section
        id='mission'
        className='py-20 bg-gradient-to-br from-gold-50 to-navy-50 dark:from-gray-800 dark:to-gray-900'
      >
        <div className='container mx-auto px-6'>
          <AnimatedSection>
            <div className='text-center mb-16'>
              <h2 className='text-3xl md:text-4xl font-bold text-navy-800 dark:text-white mb-6'>
                Mission & Vision
              </h2>
            </div>
          </AnimatedSection>

          <div className='grid md:grid-cols-2 gap-12'>
            <AnimatedSection delay={300}>
              <div className='bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-xl border border-gold-200 dark:border-gold-700'>
                <div className='w-16 h-16 bg-gradient-to-r from-navy-500 to-navy-600 rounded-2xl flex items-center justify-center mb-6'>
                  <Target className='w-8 h-8 text-white' />
                </div>
                <h3 className='text-2xl font-bold text-navy-800 dark:text-white mb-4'>
                  Our Mission
                </h3>
                <p className='text-gray-700 dark:text-gray-300 leading-relaxed'>
                  To empower refugee and vulnerable women through comprehensive
                  skills training, counseling services, and community support
                  programs that promote self-reliance, healing, and sustainable
                  peace-building in diverse communities.
                </p>
              </div>
            </AnimatedSection>

            <AnimatedSection delay={600}>
              <div className='bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-xl border border-gold-200 dark:border-gold-700'>
                <div className='w-16 h-16 bg-gradient-to-r from-gold-500 to-gold-600 rounded-2xl flex items-center justify-center mb-6'>
                  <Globe className='w-8 h-8 text-white' />
                </div>
                <h3 className='text-2xl font-bold text-navy-800 dark:text-white mb-4'>
                  Our Vision
                </h3>
                <p className='text-gray-700 dark:text-gray-300 leading-relaxed'>
                  A world where every woman, regardless of her background or
                  circumstances, has the opportunity to thrive, contribute
                  meaningfully to her community, and live with dignity, purpose,
                  and hope for a peaceful future.
                </p>
              </div>
            </AnimatedSection>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section id='values' className='py-20 bg-white dark:bg-gray-900'>
        <div className='container mx-auto px-6'>
          <AnimatedSection>
            <div className='text-center mb-16'>
              <h2 className='text-3xl md:text-4xl font-bold text-navy-800 dark:text-white mb-6'>
                Our Values
              </h2>
              <p className='text-xl text-gray-700 dark:text-gray-300 max-w-3xl mx-auto'>
                These core values guide everything we do and shape how we
                interact with the communities we serve.
              </p>
            </div>
          </AnimatedSection>

          <div className='grid md:grid-cols-2 lg:grid-cols-3 gap-8'>
            {values.map((value, index) => (
              <AnimatedSection key={index} delay={index * 200}>
                <div className='bg-gradient-to-br from-navy-50 to-gold-50 dark:from-gray-800 dark:to-gray-700 p-6 rounded-2xl hover:shadow-xl transition-all duration-300 group hover:scale-105'>
                  <div className='w-12 h-12 bg-gradient-to-r from-navy-500 to-gold-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300'>
                    <value.icon className='w-6 h-6 text-white' />
                  </div>
                  <h3 className='text-xl font-bold text-navy-800 dark:text-white mb-3'>
                    {value.title}
                  </h3>
                  <p className='text-gray-700 dark:text-gray-300 leading-relaxed'>
                    {value.description}
                  </p>
                </div>
              </AnimatedSection>
            ))}
          </div>
        </div>
      </section>
      <section
        id='about'
        className='py-20 bg-gradient-to-br from-white via-amber-50/30 to-white dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative overflow-hidden'
      >
        {/* Elegant Background Elements */}
        <div className='absolute inset-0 pointer-events-none'>
          <div className='absolute top-32 left-16 w-64 h-64 bg-gradient-to-r from-gold-300/10 to-gold-300/10 rounded-full blur-3xl animate-morph' />
          <div
            className='absolute bottom-32 right-16 w-80 h-80 bg-gradient-to-r from-navy-300/8 to-navy-400/8 rounded-full blur-3xl animate-morph'
            style={{ animationDelay: '4s' }}
          />
        </div>
        <div className='container mx-auto px-6 relative z-10'>
          <div className='max-w-6xl mx-auto'>
            {/* Section header */}
            <div className='text-center mb-24 mt-5'>
              {/* <div className='inline-flex items-center space-x-3 bg-white/80 dark:bg-navy-800/80 backdrop-blur-md rounded-full px-8 py-4 shadow-lg mb-8 border border-gold-200 dark:border-gold-700/30'>
              <span className='text-navy-800 dark:text-gold-300 font-semibold text-sm tracking-wide'>
                Our Journey
              </span>
            </div> */}
              <h2 className='font-serif text-4xl md:text-6xl font-light text-navy-800 dark:text-white mb-8 leading-tight'>
                From Refugee to
                <span className='block bg-gradient-to-r from-gold-500 via-gold-400 to-gold-500 dark:from-gold-400 dark:via-gold-300 dark:to-gold-400 bg-clip-text text-transparent'>
                  Revolutionary
                </span>
              </h2>
              <div className='w-24 h-1 bg-gradient-to-r from-amber-500 to-yellow-500 dark:from-gold-500 dark:to-gold-400 mx-auto mb-8 rounded-full'></div>
              <p className='text-elegant text-lg md:text-xl text-navy-700 dark:text-white max-w-3xl mx-auto leading-relaxed'>
                A story of courage, resilience, and the transformative power of
                believing in human potential.
              </p>
            </div>

            {/* Timeline */}
            <div className='relative mb-24'>
              <div className='hidden md:block absolute left-1/2 transform -translate-x-px w-px h-full bg-gradient-to-b from-amber-200 via-amber-300 to-amber-200'></div>

              <div className='space-y-24'>
                {/* 1997 - The Journey Begins */}
                <div className='grid md:grid-cols-2 gap-16 items-center'>
                  <div className='md:text-right'>
                    <div className='inline-block text-red-500 text-sm font-medium uppercase tracking-wider mb-4'>
                      1997 • Burundi
                    </div>
                    <h3 className='font-serif text-3xl md:text-4xl font-light text-navy-800 dark:text-gold-300 mb-6'>
                      When Home Becomes Memory
                    </h3>
                    <p className='text-navy-700 dark:text-white leading-relaxed text-lg font-light mb-6'>
                      In Burundi's hills, Mme Cimpaye Modeste was an educator
                      and beacon of hope. When conflict shattered her world, she
                      faced an impossible choice.
                    </p>
                    <blockquote className='border-l-4 border-amber-200 pl-6 italic text-navy-700 dark:text-gold-300 font-light'>
                      "I carried nothing but my diploma and my dreams."
                    </blockquote>
                  </div>
                  <div className='relative'>
                    <div className='hidden md:block absolute -left-8 top-12 w-4 h-4 bg-red-400 rounded-full border-4 border-white shadow-lg'></div>
                    <div className='relative overflow-hidden rounded-2xl'>
                      <img
                        src='/assets/map.png'
                        alt='Journey from Burundi'
                        className='w-full h-80 object-cover'
                      />
                      <div className='absolute inset-0 bg-black/20'></div>
                    </div>
                  </div>
                </div>

                {/* 1999 - The Awakening */}
                <div className='grid md:grid-cols-2 gap-16 items-center'>
                  <div className='md:order-2'>
                    <div className='inline-block text-amber-500 text-sm font-medium uppercase tracking-wider mb-4'>
                      1999 • Kayole, Kenya
                    </div>
                    <h3 className='font-serif text-3xl md:text-4xl font-light text-navy-800 dark:text-gold-300 mb-6'>
                      Where Despair Meets Determination
                    </h3>
                    <p className='text-navy-700 dark:text-white leading-relaxed text-lg font-light mb-6'>
                      In Kayole settlement, Modeste witnessed brilliant women
                      reduced by circumstance. Where others saw hopelessness,
                      she saw untapped potential.
                    </p>
                    <div className='bg-amber-50 border-l-4 border-amber-300 p-6 rounded-r'>
                      <p className='text-navy-700 dark:text-amber-700 italic font-light'>
                        "What if we didn't have to beg? What if we could
                        create?"
                      </p>
                    </div>
                  </div>
                  <div className='relative md:order-1'>
                    <div className='hidden md:block absolute -right-8 top-12 w-4 h-4 bg-amber-400 rounded-full border-4 border-white shadow-lg'></div>
                    <div className='relative overflow-hidden rounded-2xl'>
                      <img
                        src='/assets/training1.jpg'
                        alt='Community awakening'
                        className='w-full h-80 object-cover'
                      />
                      <div className='absolute inset-0 bg-black/20'></div>
                    </div>
                  </div>
                </div>

                {/* 1999 - WOPEDE Birth */}
                <div className='grid md:grid-cols-2 gap-16 items-center'>
                  <div className='md:text-right'>
                    <div className='inline-block text-blue-500 text-sm font-medium uppercase tracking-wider mb-4'>
                      1999 • WOPEDE Founded
                    </div>
                    <h3 className='font-serif text-3xl md:text-4xl font-light text-navy-800 dark:text-gold-300 mb-6'>
                      When Dreams Take Flight
                    </h3>
                    <p className='text-navy-700 dark:text-white  leading-relaxed text-lg font-light mb-6'>
                      WOPEDE was forged in necessity and tempered by hope. Over
                      100 women gathered, each bringing their skills and
                      determination to rewrite their destinies.
                    </p>
                    <div className='flex justify-end'>
                      <div className='grid grid-cols-2 gap-6'>
                        <div className='text-center'>
                          <div className='text-3xl font-serif font-light text-blue-500'>
                            100+
                          </div>
                          <div className='text-xs uppercase tracking-wider text-gray-500'>
                            Founding Members
                          </div>
                        </div>
                        <div className='text-center'>
                          <div className='text-3xl font-serif font-light text-blue-500'>
                            ∞
                          </div>
                          <div className='text-xs uppercase tracking-wider text-gray-500'>
                            Dreams Ignited
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className='relative'>
                    <div className='hidden md:block absolute -left-8 top-12 w-4 h-4 bg-blue-400 rounded-full border-4 border-white shadow-lg'></div>
                    <div className='relative overflow-hidden rounded-2xl'>
                      <img
                        src='/assets/waving.jpg'
                        alt='WOPEDE foundation'
                        className='w-full h-80 object-cover'
                      />
                      <div className='absolute inset-0 bg-black/20'></div>
                    </div>
                  </div>
                </div>

                {/* 2022 - Phoenix Rising */}
                <div className='grid md:grid-cols-2 gap-16 items-center'>
                  <div className='md:order-2'>
                    <div className='inline-block text-green-500 text-sm font-medium uppercase tracking-wider mb-4'>
                      2022 • Reborn Stronger
                    </div>
                    <h3 className='font-serif text-3xl md:text-4xl font-light text-navy-800 dark:text-gold-300 mb-6'>
                      The Phoenix Rises
                    </h3>
                    <p className='text-navy-700 dark:text-white  leading-relaxed text-lg font-light mb-6'>
                      After years of challenges, Modeste proved that true vision
                      never dies. WOPEDE was reborn, stronger and more
                      determined than ever.
                    </p>
                    <div className='space-y-2'>
                      <div className='flex items-center text-green-600'>
                        <div className='w-2 h-2 bg-green-400 rounded-full mr-3'></div>
                        <span className='text-sm'>Survivors of violence</span>
                      </div>
                      <div className='flex items-center text-green-600'>
                        <div className='w-2 h-2 bg-green-400 rounded-full mr-3'></div>
                        <span className='text-sm'>Refugee women & girls</span>
                      </div>
                      <div className='flex items-center text-green-600'>
                        <div className='w-2 h-2 bg-green-400 rounded-full mr-3'></div>
                        <span className='text-sm'>Community partnerships</span>
                      </div>
                    </div>
                  </div>
                  <div className='relative md:order-1'>
                    <div className='hidden md:block absolute -right-8 top-12 w-4 h-4 bg-green-400 rounded-full border-4 border-white shadow-lg'></div>
                    <div className='relative overflow-hidden rounded-2xl'>
                      <img
                        src='/assets/stakeholders.jpg'
                        alt='Modern WOPEDE'
                        className='w-full h-80 object-cover'
                      />
                      <div className='absolute inset-0 bg-black/20'></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Founder spotlight */}
            <div className='bg-gradient-to-br from-amber-50 to-yellow-50 dark:from-navy-800/90 dark:to-navy rounded-3xl shadow-xl overflow-hidden mb-20 border border-amber-100 dark:border-gold-700/20'>
              <div className='flex flex-col lg:flex-row'>
                <div className='lg:w-2/5'>
                  <img
                    src='/assets/founder1.jpg'
                    alt='Cimpaye Modeste'
                    className='w-full h-96 lg:h-object-fit'
                  />
                </div>
                <div className='lg:w-3/5 p-12'>
                  <div className='mb-8'>
                    <h3 className='font-serif text-4xl font-light text-navy-800 dark:text-white mb-2'>
                      Cimpaye Modeste
                    </h3>
                    <p className='text-amber-400 font-medium mb-1'>
                      Founder & Visionary
                    </p>
                    <p className='text-gray-400 text-sm'>
                      Educator • Refugee • Revolutionary
                    </p>
                  </div>

                  <blockquote className='text-lg leading-relaxed italic text-navy-700 dark:text-white font-light border-l-4 border-amber-200 pl-6 mb-8'>
                    "Every woman carries within her the power to transform not
                    just her own life, but the lives of everyone around her."
                  </blockquote>

                  <div className='flex flex-wrap gap-3'>
                    <span className='px-4 py-2 bg-amber-50 text-amber-700 rounded-full text-sm'>
                      25+ Years Experience
                    </span>
                    <span className='px-4 py-2 bg-blue-50 text-blue-700 rounded-full text-sm'>
                      Education Leader
                    </span>
                    <span className='px-4 py-2 bg-green-50 text-green-700 rounded-full text-sm'>
                      Community Builder
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* Team Section */}
      <section
        id='team'
        className='py-20 bg-gradient-to-br from-navy-50 via-white to-gold-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900'
      >
        <div className='container mx-auto px-6'>
          <AnimatedSection>
            <div className='text-center mb-16'>
              <h2 className='text-3xl md:text-4xl font-bold text-navy-800 dark:text-white mb-6'>
                Meet Our Team
              </h2>
              <p className='text-xl text-gray-700 dark:text-gray-300 max-w-3xl mx-auto'>
                Dedicated leaders working tirelessly to empower women and build
                stronger communities.
              </p>
            </div>
          </AnimatedSection>

          <div className='grid md:grid-cols-2 lg:grid-cols-3 gap-8'>
            {team.map((member, index) => (
              <AnimatedSection key={index} delay={index * 300}>
                <div className='bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 group hover:scale-105'>
                  <div className='aspect-square overflow-hidden'>
                    <img
                      src={member.image}
                      alt={member.name}
                      className='w-full h-full object-cover group-hover:scale-110 transition-transform duration-500'
                    />
                  </div>
                  <div className='p-6'>
                    <h3 className='text-xl font-bold text-navy-800 dark:text-white mb-2'>
                      {member.name}
                    </h3>
                    <p className='text-gold-600 dark:text-gold-400 font-semibold mb-3'>
                      {member.role}
                    </p>
                    <p className='text-gray-700 dark:text-gray-300 text-sm leading-relaxed'>
                      {member.bio}
                    </p>
                  </div>
                </div>
              </AnimatedSection>
            ))}
          </div>
        </div>
      </section>

      <GallerySection />
      <Footer />
    </div>
  );
};

export default About;
