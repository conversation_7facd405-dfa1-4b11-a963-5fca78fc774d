import React from 'react';
import { Heart, Users, Target, Award, Globe, Lightbulb } from 'lucide-react';
import AnimatedSection from '@/components/AnimatedSection';

const About = () => {
  const values = [
    {
      icon: Heart,
      title: "Compassion",
      description: "We approach every woman with empathy, understanding their unique challenges and providing support with genuine care."
    },
    {
      icon: Users,
      title: "Community",
      description: "Building strong networks of support where women can connect, learn from each other, and grow together."
    },
    {
      icon: Target,
      title: "Empowerment",
      description: "Providing tools, skills, and opportunities that enable women to become self-reliant and confident leaders."
    },
    {
      icon: Award,
      title: "Excellence",
      description: "Maintaining high standards in all our programs and services to ensure maximum impact and lasting change."
    },
    {
      icon: Globe,
      title: "Inclusivity",
      description: "Welcoming women from all backgrounds, cultures, and circumstances without discrimination or judgment."
    },
    {
      icon: Lightbulb,
      title: "Innovation",
      description: "Continuously evolving our approaches and methods to meet the changing needs of the communities we serve."
    }
  ];

  const team = [
    {
      name: "<PERSON><PERSON><PERSON><PERSON>",
      role: "Founder & Executive Director",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=center",
      bio: "With over 25 years of experience in community development, <PERSON><PERSON> founded WOPEDE with a vision to empower refugee women and build lasting peace."
    },
    {
      name: "<PERSON> Mukamana",
      role: "Program Director",
      image: "https://images.unsplash.com/photo-1580489944761-15a19d654956?w=300&h=300&fit=crop&crop=center",
      bio: "Sarah oversees all training programs and has helped design curricula that have transformed hundreds of women's lives."
    },
    {
      name: "Grace Wanjiku",
      role: "Community Outreach Coordinator",
      image: "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=300&h=300&fit=crop&crop=center",
      bio: "Grace builds bridges between communities and ensures our programs reach those who need them most."
    }
  ];

  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-navy-50 via-white to-gold-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="container mx-auto px-6">
          <AnimatedSection>
            <div className="text-center mb-16">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-navy-800 dark:text-white mb-6">
                About <span className="bg-gradient-to-r from-gold-500 to-navy-600 bg-clip-text text-transparent">WOPEDE</span>
              </h1>
              <p className="text-xl text-gray-700 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
                For over 25 years, we've been dedicated to empowering women, building peace, and creating sustainable change in communities across Kenya.
              </p>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Our Story Section */}
      <section id="story" className="py-20 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-6">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <AnimatedSection>
              <div>
                <h2 className="text-3xl md:text-4xl font-bold text-navy-800 dark:text-white mb-6">
                  Our Story
                </h2>
                <div className="space-y-6 text-gray-700 dark:text-gray-300 leading-relaxed">
                  <p>
                    WOPEDE (Women's Organization for Peace and Development) was founded in 1999 by Mme Cimpaye Modeste, 
                    a visionary leader who recognized the urgent need to support refugee women and promote peace in communities 
                    affected by conflict.
                  </p>
                  <p>
                    What started as a small initiative to help a handful of women has grown into a comprehensive organization 
                    that has transformed the lives of over 1,000 women through skills training, counseling, and community 
                    support programs.
                  </p>
                  <p>
                    Our journey has been one of resilience, hope, and unwavering commitment to the belief that when women 
                    are empowered, entire communities thrive. Today, we continue to expand our reach and impact, always 
                    staying true to our founding principles of compassion, empowerment, and peace-building.
                  </p>
                </div>
              </div>
            </AnimatedSection>

            <AnimatedSection delay={300}>
              <div className="relative">
                <img
                  src="https://images.unsplash.com/photo-1559027615-cd4628902d4a?w=600&h=400&fit=crop&crop=center"
                  alt="WOPEDE community gathering"
                  className="rounded-2xl shadow-2xl"
                />
                <div className="absolute -bottom-6 -right-6 bg-gradient-to-r from-gold-500 to-gold-400 text-white p-6 rounded-2xl shadow-xl">
                  <div className="text-2xl font-bold">1999</div>
                  <div className="text-sm">Founded</div>
                </div>
              </div>
            </AnimatedSection>
          </div>
        </div>
      </section>

      {/* Mission & Vision Section */}
      <section id="mission" className="py-20 bg-gradient-to-br from-gold-50 to-navy-50 dark:from-gray-800 dark:to-gray-900">
        <div className="container mx-auto px-6">
          <AnimatedSection>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-navy-800 dark:text-white mb-6">
                Mission & Vision
              </h2>
            </div>
          </AnimatedSection>

          <div className="grid md:grid-cols-2 gap-12">
            <AnimatedSection delay={300}>
              <div className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-xl border border-gold-200 dark:border-gold-700">
                <div className="w-16 h-16 bg-gradient-to-r from-navy-500 to-navy-600 rounded-2xl flex items-center justify-center mb-6">
                  <Target className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-navy-800 dark:text-white mb-4">Our Mission</h3>
                <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                  To empower refugee and vulnerable women through comprehensive skills training, counseling services, 
                  and community support programs that promote self-reliance, healing, and sustainable peace-building 
                  in diverse communities.
                </p>
              </div>
            </AnimatedSection>

            <AnimatedSection delay={600}>
              <div className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-xl border border-gold-200 dark:border-gold-700">
                <div className="w-16 h-16 bg-gradient-to-r from-gold-500 to-gold-600 rounded-2xl flex items-center justify-center mb-6">
                  <Globe className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-navy-800 dark:text-white mb-4">Our Vision</h3>
                <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                  A world where every woman, regardless of her background or circumstances, has the opportunity to 
                  thrive, contribute meaningfully to her community, and live with dignity, purpose, and hope for 
                  a peaceful future.
                </p>
              </div>
            </AnimatedSection>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section id="values" className="py-20 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-6">
          <AnimatedSection>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-navy-800 dark:text-white mb-6">
                Our Values
              </h2>
              <p className="text-xl text-gray-700 dark:text-gray-300 max-w-3xl mx-auto">
                These core values guide everything we do and shape how we interact with the communities we serve.
              </p>
            </div>
          </AnimatedSection>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {values.map((value, index) => (
              <AnimatedSection key={index} delay={index * 200}>
                <div className="bg-gradient-to-br from-navy-50 to-gold-50 dark:from-gray-800 dark:to-gray-700 p-6 rounded-2xl hover:shadow-xl transition-all duration-300 group hover:scale-105">
                  <div className="w-12 h-12 bg-gradient-to-r from-navy-500 to-gold-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                    <value.icon className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-navy-800 dark:text-white mb-3">{value.title}</h3>
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">{value.description}</p>
                </div>
              </AnimatedSection>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section id="team" className="py-20 bg-gradient-to-br from-navy-50 via-white to-gold-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="container mx-auto px-6">
          <AnimatedSection>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-navy-800 dark:text-white mb-6">
                Meet Our Team
              </h2>
              <p className="text-xl text-gray-700 dark:text-gray-300 max-w-3xl mx-auto">
                Dedicated leaders working tirelessly to empower women and build stronger communities.
              </p>
            </div>
          </AnimatedSection>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {team.map((member, index) => (
              <AnimatedSection key={index} delay={index * 300}>
                <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 group hover:scale-105">
                  <div className="aspect-square overflow-hidden">
                    <img
                      src={member.image}
                      alt={member.name}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                  </div>
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-navy-800 dark:text-white mb-2">{member.name}</h3>
                    <p className="text-gold-600 dark:text-gold-400 font-semibold mb-3">{member.role}</p>
                    <p className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">{member.bio}</p>
                  </div>
                </div>
              </AnimatedSection>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;