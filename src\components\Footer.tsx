import React from 'react';
import { Mail, Phone, MapPin, Facebook, Twitter, Instagram, Linkedin, Heart } from 'lucide-react';
import { Link } from 'react-router-dom';

const Footer = () => {
  return (
    <footer className='bg-gray-900 text-white'>
      <div className='container mx-auto px-6 py-16'>
        <div className='grid md:grid-cols-2 lg:grid-cols-4 gap-8'>
          {/* About Section */}
          <div className='space-y-4'>
            <h3 className='text-2xl font-bold text-amber-400'>WOPEDE</h3>
            <p className='text-gray-300 leading-relaxed'>
              Empowering women through sustainable income opportunities, skills development, and community building for lasting peace and development.
            </p>
            <div className='flex space-x-4'>
              <a href='#' className='w-10 h-10 bg-amber-600 rounded-full flex items-center justify-center hover:bg-amber-700 transition-colors duration-300'>
                <Facebook className='w-5 h-5' />
              </a>
              <a href='#' className='w-10 h-10 bg-amber-600 rounded-full flex items-center justify-center hover:bg-amber-700 transition-colors duration-300'>
                <Twitter className='w-5 h-5' />
              </a>
              <a href='#' className='w-10 h-10 bg-amber-600 rounded-full flex items-center justify-center hover:bg-amber-700 transition-colors duration-300'>
                <Instagram className='w-5 h-5' />
              </a>
              <a href='#' className='w-10 h-10 bg-amber-600 rounded-full flex items-center justify-center hover:bg-amber-700 transition-colors duration-300'>
                <Linkedin className='w-5 h-5' />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className='space-y-4'>
            <h4 className='text-lg font-semibold text-amber-400'>Quick Links</h4>
            <ul className='space-y-2'>
              <li><Link to='/' className='text-gray-300 hover:text-amber-400 transition-colors duration-300'>Home</Link></li>
              <li><Link to='/about' className='text-gray-300 hover:text-amber-400 transition-colors duration-300'>About Us</Link></li>
              <li><Link to='/programs' className='text-gray-300 hover:text-amber-400 transition-colors duration-300'>Programs</Link></li>
              <li><Link to='/products' className='text-gray-300 hover:text-amber-400 transition-colors duration-300'>Products</Link></li>
              <li><Link to='/gallery' className='text-gray-300 hover:text-amber-400 transition-colors duration-300'>Gallery</Link></li>
              <li><Link to='/contact' className='text-gray-300 hover:text-amber-400 transition-colors duration-300'>Contact</Link></li>
            </ul>
          </div>

          {/* Get Involved */}
          <div className='space-y-4'>
            <h4 className='text-lg font-semibold text-amber-400'>Get Involved</h4>
            <ul className='space-y-2'>
              <li><Link to='/contact' className='text-gray-300 hover:text-amber-400 transition-colors duration-300'>Volunteer with Us</Link></li>
              <li><Link to='/contact' className='text-gray-300 hover:text-amber-400 transition-colors duration-300'>Make a Donation</Link></li>
              <li><Link to='/contact' className='text-gray-300 hover:text-amber-400 transition-colors duration-300'>Partner with WOPEDE</Link></li>
              <li><Link to='/contact' className='text-gray-300 hover:text-amber-400 transition-colors duration-300'>Sponsor a Program</Link></li>
              <li><Link to='/contact' className='text-gray-300 hover:text-amber-400 transition-colors duration-300'>Spread the Word</Link></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className='space-y-4'>
            <h4 className='text-lg font-semibold text-amber-400'>Contact Us</h4>
            <div className='space-y-3'>
              <div className='flex items-center space-x-3'>
                <MapPin className='w-5 h-5 text-amber-400' />
                <span className='text-gray-300'>Kakuma Refugee Camp, Kenya</span>
              </div>
              <div className='flex items-center space-x-3'>
                <Phone className='w-5 h-5 text-amber-400' />
                <span className='text-gray-300'>+254 XXX XXX XXX</span>
              </div>
              <div className='flex items-center space-x-3'>
                <Mail className='w-5 h-5 text-amber-400' />
                <span className='text-gray-300'><EMAIL></span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className='border-t border-gray-800 mt-12 pt-8'>
          <div className='text-center mb-4'>
            <p className='text-amber-400 font-semibold text-lg mb-2'>
              "Empowering Women, Transforming Communities, Building Peace."
            </p>
          </div>
          <div className='flex flex-col md:flex-row justify-between items-center'>
            <p className='text-gray-400 text-sm'>
              © {new Date().getFullYear()} WOPEDE CBO. All rights reserved. | Women for Peace and Development Community-Based Organization
            </p>
            <div className='flex items-center space-x-2 mt-4 md:mt-0'>
              <span className='text-gray-400 text-sm'>Made with</span>
              <Heart className='w-4 h-4 text-red-500' />
              <span className='text-gray-400 text-sm'>for empowering women</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;