import React, { useState } from 'react';
import { Mail, Send } from 'lucide-react';

const Newsletter = () => {
  const [email, setEmail] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (email) {
      setIsSubscribed(true);
      setEmail('');
      setTimeout(() => setIsSubscribed(false), 3000);
    }
  };

  return (
    <section className='py-20 bg-gradient-to-r from-amber-600 to-yellow-600'>
      <div className='container mx-auto px-6'>
        <div className='max-w-4xl mx-auto text-center'>
          <div className='flex items-center justify-center mb-6'>
            <Mail className='w-8 h-8 text-white mr-4' />
            <h2 className='text-3xl md:text-4xl font-bold text-white'>Stay Connected</h2>
          </div>
          
          <p className='text-xl text-white/90 mb-8 leading-relaxed'>
            Get the latest updates on our programs, inspiring success stories, and opportunities to make a difference in women's lives.
          </p>

          <form onSubmit={handleSubmit} className='max-w-md mx-auto'>
            <div className='flex flex-col sm:flex-row gap-4'>
              <input
                type='email'
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder='Enter your email address'
                className='flex-1 px-6 py-4 rounded-xl border-0 focus:ring-4 focus:ring-white/30 focus:outline-none text-gray-800 placeholder-gray-500'
                required
              />
              <button
                type='submit'
                className='bg-white text-amber-600 px-8 py-4 rounded-xl font-semibold hover:bg-gray-100 transition-all duration-300 flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl transform hover:scale-105'
              >
                <Send className='w-5 h-5' />
                <span>Subscribe</span>
              </button>
            </div>
          </form>

          {isSubscribed && (
            <div className='mt-6 bg-white/20 backdrop-blur-sm text-white px-6 py-3 rounded-xl inline-block'>
              Thank you for subscribing! Welcome to our community.
            </div>
          )}

          <p className='text-white/70 text-sm mt-6'>
            We respect your privacy. Unsubscribe at any time.
          </p>
        </div>
      </div>
    </section>
  );
};

export default Newsletter;