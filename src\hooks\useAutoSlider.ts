
import { useState, useEffect, useCallback } from 'react';

interface UseAutoSliderOptions {
  itemCount: number;
  interval?: number;
  autoPlay?: boolean;
}

export const useAutoSlider = ({
  itemCount,
  interval = 4000,
  autoPlay = true
}: UseAutoSliderOptions) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(autoPlay);

  const next = useCallback(() => {
    setCurrentIndex((prev) => (prev + 1) % itemCount);
  }, [itemCount]);

  const previous = useCallback(() => {
    setCurrentIndex((prev) => (prev - 1 + itemCount) % itemCount);
  }, [itemCount]);

  const goTo = useCallback((index: number) => {
    setCurrentIndex(index);
  }, []);

  const toggleAutoPlay = useCallback(() => {
    setIsAutoPlaying((prev) => !prev);
  }, []);

  useEffect(() => {
    if (!isAutoPlaying) return;

    const intervalId = setInterval(next, interval);
    return () => clearInterval(intervalId);
  }, [isAutoPlaying, next, interval]);

  return {
    currentIndex,
    isAutoPlaying,
    next,
    previous,
    goTo,
    toggleAutoPlay
  };
};
