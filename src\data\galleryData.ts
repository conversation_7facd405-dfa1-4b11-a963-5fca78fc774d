import { images } from '@/utils/images';

export interface GalleryImage {
  id: string;
  title: string;
  description: string;
  image: string;
  category: string;
  date?: string;
  location?: string;
  photographer?: string;
  tags: string[];
}

export interface GalleryCategory {
  id: string;
  name: string;
  description: string;
  coverImage: string;
  images: GalleryImage[];
}

// Gallery Images Data
export const galleryImages: GalleryImage[] = [
  // Training & Education
  {
    id: 'training-001',
    title: 'Skills Training Session',
    description:
      'Women participating in a comprehensive skills training program, learning new techniques and building confidence.',
    image: '/assets/training1.jpg',
    category: 'training',
    date: '2024',
    location: 'WOPEDE Training Center',
    tags: ['training', 'education', 'skills', 'empowerment'],
  },
  {
    id: 'training-002',
    title: 'Sewing Workshop',
    description:
      'Participants learning professional sewing techniques in our well-equipped workshop.',
    image: '/assets/sewing.jpg',
    category: 'training',
    date: '2024',
    location: 'Tailoring Workshop',
    tags: ['sewing', 'tailoring', 'workshop', 'skills'],
  },
  {
    id: 'training-003',
    title: 'Group Learning Session',
    description:
      'Collaborative learning environment where women share knowledge and support each other.',
    image: images.programs.training.general2,
    category: 'training',
    date: '2024',
    location: 'Community Center',
    tags: ['group', 'learning', 'collaboration', 'support'],
  },
  {
    id: 'training-004',
    title: 'Practical Skills Development',
    description:
      'Hands-on training session focusing on practical skills for economic independence.',
    image: images.programs.training.general3,
    category: 'training',
    date: '2024',
    location: 'Skills Center',
    tags: ['practical', 'skills', 'hands-on', 'independence'],
  },

  // Business & Entrepreneurship
  {
    id: 'business-001',
    title: 'Small Business Success',
    description:
      'Successful women entrepreneurs showcasing their thriving businesses.',
    image: images.business.success.bus5,
    category: 'business',
    date: '2024',
    location: 'Local Market',
    tags: ['business', 'success', 'entrepreneurship', 'market'],
  },
  {
    id: 'business-002',
    title: 'Market Day',
    description:
      'Women selling their products at the local market, demonstrating economic empowerment.',
    image: images.business.market.selling,
    category: 'business',
    date: '2024',
    location: 'Community Market',
    tags: ['market', 'selling', 'products', 'economic'],
  },
  {
    id: 'business-003',
    title: 'Product Display',
    description:
      'Beautiful display of handmade products created by WOPEDE beneficiaries.',
    image: images.business.market.display,
    category: 'business',
    date: '2024',
    location: 'Exhibition Hall',
    tags: ['products', 'display', 'handmade', 'exhibition'],
  },
  {
    id: 'business-004',
    title: 'Business Development',
    description:
      'Women participating in business development and entrepreneurship training.',
    image: images.business.development.bus1,
    category: 'business',
    date: '2024',
    location: 'Training Center',
    tags: ['development', 'entrepreneurship', 'training', 'business'],
  },

  // Arts & Crafts
  {
    id: 'arts-001',
    title: 'Traditional Crafts',
    description:
      'Beautiful traditional crafts created by skilled artisans in our programs.',
    image: images.arts.products.art1,
    category: 'arts',
    date: '2024',
    location: 'Arts Workshop',
    tags: ['crafts', 'traditional', 'artisans', 'culture'],
  },
  {
    id: 'arts-002',
    title: 'Basket Weaving',
    description:
      'Traditional basket weaving techniques being practiced and preserved.',
    image: images.arts.products.baskets,
    category: 'arts',
    date: '2024',
    location: 'Craft Center',
    tags: ['baskets', 'weaving', 'traditional', 'techniques'],
  },
  {
    id: 'arts-003',
    title: 'Art Exhibition',
    description:
      'Community art exhibition showcasing the creative talents of our participants.',
    image: images.arts.exhibition.display,
    category: 'arts',
    date: '2024',
    location: 'Community Gallery',
    tags: ['exhibition', 'art', 'creative', 'showcase'],
  },

  // Community Events
  {
    id: 'community-001',
    title: 'Community Gathering',
    description:
      'Large community gathering bringing together women from diverse backgrounds.',
    image: images.community.events.gathering,
    category: 'community',
    date: '2024',
    location: 'Community Hall',
    tags: ['community', 'gathering', 'diverse', 'unity'],
  },
  {
    id: 'community-002',
    title: 'Stakeholder Meeting',
    description:
      'Important stakeholder meeting discussing program development and community needs.',
    image: images.community.events.meeting,
    category: 'community',
    date: '2024',
    location: 'Conference Room',
    tags: ['stakeholders', 'meeting', 'development', 'planning'],
  },
  {
    id: 'community-003',
    title: 'Celebration Event',
    description:
      'Joyful celebration of achievements and milestones in our community programs.',
    image: images.community.events.celebration,
    category: 'community',
    date: '2024',
    location: 'Community Center',
    tags: ['celebration', 'achievements', 'milestones', 'joy'],
  },

  // Leadership & Founder
  {
    id: 'founder-001',
    title: 'Founder Portrait',
    description: 'Mme Cimpaye Modeste, founder and visionary leader of WOPEDE.',
    image: images.founder.main,
    category: 'leadership',
    date: '2024',
    location: 'WOPEDE Office',
    tags: ['founder', 'leadership', 'visionary', 'modeste'],
  },
  {
    id: 'founder-002',
    title: 'Community Address',
    description:
      'Founder addressing the community and sharing the vision of WOPEDE.',
    image: images.founder.speaking,
    category: 'leadership',
    date: '2024',
    location: 'Community Event',
    tags: ['address', 'community', 'vision', 'leadership'],
  },
];

// Gallery Categories
export const galleryCategories: GalleryCategory[] = [
  {
    id: 'training',
    name: 'Training & Education',
    description:
      'Images from our comprehensive training programs and educational initiatives.',
    coverImage: images.programs.training.general1,
    images: galleryImages.filter((img) => img.category === 'training'),
  },
  {
    id: 'business',
    name: 'Business & Entrepreneurship',
    description:
      'Success stories and business development activities of our beneficiaries.',
    coverImage: images.business.success.bus5,
    images: galleryImages.filter((img) => img.category === 'business'),
  },
  {
    id: 'arts',
    name: 'Arts & Crafts',
    description:
      'Beautiful arts and crafts created by talented artisans in our programs.',
    coverImage: images.arts.products.art1,
    images: galleryImages.filter((img) => img.category === 'arts'),
  },
  {
    id: 'community',
    name: 'Community Events',
    description: 'Community gatherings, celebrations, and important events.',
    coverImage: images.community.events.gathering,
    images: galleryImages.filter((img) => img.category === 'community'),
  },
  {
    id: 'leadership',
    name: 'Leadership',
    description:
      'Our leadership team and founder making a difference in the community.',
    coverImage: images.founder.main,
    images: galleryImages.filter((img) => img.category === 'leadership'),
  },
];

// Featured Gallery Images
export const featuredImages = galleryImages.filter((img) =>
  [
    'training-001',
    'business-001',
    'arts-001',
    'community-001',
    'founder-001',
  ].includes(img.id)
);

// Recent Images (last 10)
export const recentImages = galleryImages.slice(-10);

// Gallery Statistics
export const galleryStats = {
  totalImages: galleryImages.length,
  totalCategories: galleryCategories.length,
  trainingImages: galleryImages.filter((img) => img.category === 'training')
    .length,
  businessImages: galleryImages.filter((img) => img.category === 'business')
    .length,
  artsImages: galleryImages.filter((img) => img.category === 'arts').length,
  communityImages: galleryImages.filter((img) => img.category === 'community')
    .length,
};
