import React from 'react';
import {
  TrendingUp,
  Users,
  Award,
  Heart,
  Target,
  Globe,
  Calendar,
  MapPin,
} from 'lucide-react';
import AnimatedSection from '@/components/AnimatedSection';
import GallerySection from '@/components/GallerySection';
import Footer from '@/components/Footer';

const Impact = () => {
  const impactStats = [
    {
      icon: Users,
      number: '1,247',
      title: 'Women Empowered',
      description: 'Lives transformed through our comprehensive programs',
      color: 'from-blue-500 to-blue-600',
    },
    {
      icon: Award,
      number: '95%',
      title: 'Success Rate',
      description: 'Program completion and employment success',
      color: 'from-green-500 to-green-600',
    },
    {
      icon: Globe,
      number: '15',
      title: 'Communities Served',
      description: 'Across Kenya and East Africa region',
      color: 'from-purple-500 to-purple-600',
    },
    {
      icon: Heart,
      number: '3,500+',
      title: 'Family Members',
      description: 'Indirectly benefited from our programs',
      color: 'from-red-500 to-red-600',
    },
  ];

  const successStories = [
    {
      name: '<PERSON><PERSON>',
      age: 32,
      program: 'Tailoring & Fashion Design',
      story:
        'After completing our 6-month tailoring program, <PERSON><PERSON> started her own clothing business. She now employs 3 other women and supports her family of 5.',
      image:
        'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=center',
      achievement: 'Business Owner',
      year: '2023',
    },
    {
      name: 'Grace Wanjiku',
      age: 28,
      program: 'Digital Literacy & Computer Skills',
      story:
        "Grace learned computer skills and now works as a data entry clerk for a local NGO. She's also teaching other women basic computer skills.",
      image:
        'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=300&h=300&fit=crop&crop=center',
      achievement: 'IT Professional',
      year: '2023',
    },
    {
      name: 'Sarah Nyong',
      age: 35,
      program: 'Culinary Arts & Catering',
      story:
        'Sarah transformed her passion for cooking into a thriving catering business. She now provides meals for local events and has trained 5 apprentices.',
      image:
        'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=300&h=300&fit=crop&crop=center',
      achievement: 'Chef & Entrepreneur',
      year: '2022',
    },
  ];

  const yearlyImpact = [
    { year: '2020', women: 180, businesses: 45, employment: 78 },
    { year: '2021', women: 220, businesses: 62, employment: 95 },
    { year: '2022', women: 285, businesses: 78, employment: 125 },
    { year: '2023', women: 340, businesses: 95, employment: 165 },
    { year: '2024', women: 222, businesses: 58, employment: 98 },
  ];

  const sdgGoals = [
    {
      number: 1,
      title: 'No Poverty',
      description: 'Creating sustainable income opportunities',
    },
    {
      number: 4,
      title: 'Quality Education',
      description: 'Providing skills training and literacy programs',
    },
    {
      number: 5,
      title: 'Gender Equality',
      description: 'Empowering women and promoting equality',
    },
    {
      number: 8,
      title: 'Decent Work',
      description: 'Creating employment and entrepreneurship opportunities',
    },
    {
      number: 16,
      title: 'Peace & Justice',
      description: 'Building peaceful and inclusive communities',
    },
  ];

  return (
    <div className='min-h-screen pt-20'>
      {/* Hero Section */}
      <section className='py-20 bg-gradient-to-br from-navy-50 via-white to-gold-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900'>
        <div className='container mx-auto px-6'>
          <AnimatedSection>
            <div className='text-center mb-16'>
              <h1 className='text-4xl md:text-5xl lg:text-6xl font-bold text-navy-800 dark:text-white mb-6'>
                Our{' '}
                <span className='bg-gradient-to-r from-gold-500 to-navy-600 bg-clip-text text-transparent'>
                  Impact
                </span>
              </h1>
              <p className='text-xl text-gray-700 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed'>
                Measuring the real difference we make in women's lives and
                communities through data, stories, and sustainable change.
              </p>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Impact Statistics */}
      <section className='py-20 bg-white dark:bg-gray-900'>
        <div className='container mx-auto px-6'>
          <AnimatedSection>
            <div className='text-center mb-16'>
              <h2 className='text-3xl md:text-4xl font-bold text-navy-800 dark:text-white mb-6'>
                Impact by Numbers
              </h2>
              <p className='text-lg text-gray-700 dark:text-gray-300 max-w-2xl mx-auto'>
                Quantifying our commitment to women's empowerment and community
                development.
              </p>
            </div>
          </AnimatedSection>

          <div className='grid md:grid-cols-2 lg:grid-cols-4 gap-8'>
            {impactStats.map((stat, index) => (
              <AnimatedSection key={index} delay={index * 200}>
                <div className='bg-gradient-to-br from-navy-50 to-gold-50 dark:from-gray-800 dark:to-gray-700 p-8 rounded-2xl hover:shadow-xl transition-all duration-300 group hover:scale-105 text-center'>
                  <div
                    className={`w-16 h-16 bg-gradient-to-r ${stat.color} rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <stat.icon className='w-8 h-8 text-white' />
                  </div>
                  <div className='text-4xl font-bold text-navy-800 dark:text-white mb-2 group-hover:animate-pulse'>
                    {stat.number}
                  </div>
                  <h3 className='text-xl font-bold text-navy-800 dark:text-white mb-3'>
                    {stat.title}
                  </h3>
                  <p className='text-gray-700 dark:text-gray-300 leading-relaxed'>
                    {stat.description}
                  </p>
                </div>
              </AnimatedSection>
            ))}
          </div>
        </div>
      </section>

      {/* Success Stories */}
      <section className='py-20 bg-gradient-to-br from-gold-50 to-navy-50 dark:from-gray-800 dark:to-gray-900'>
        <div className='container mx-auto px-6'>
          <AnimatedSection>
            <div className='text-center mb-16'>
              <h2 className='text-3xl md:text-4xl font-bold text-navy-800 dark:text-white mb-6'>
                Success Stories
              </h2>
              <p className='text-lg text-gray-700 dark:text-gray-300 max-w-2xl mx-auto'>
                Real women, real transformations, real impact on communities.
              </p>
            </div>
          </AnimatedSection>

          <div className='grid lg:grid-cols-3 gap-8'>
            {successStories.map((story, index) => (
              <AnimatedSection key={index} delay={index * 300}>
                <div className='bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 group hover:scale-105'>
                  <div className='aspect-square overflow-hidden'>
                    <img
                      src={story.image}
                      alt={story.name}
                      className='w-full h-full object-cover group-hover:scale-110 transition-transform duration-500'
                    />
                  </div>
                  <div className='p-6'>
                    <div className='flex items-center justify-between mb-4'>
                      <div>
                        <h3 className='text-xl font-bold text-navy-800 dark:text-white'>
                          {story.name}
                        </h3>
                        <p className='text-gray-600 dark:text-gray-400 text-sm'>
                          Age {story.age} • {story.year}
                        </p>
                      </div>
                      <div className='bg-gradient-to-r from-gold-500 to-gold-600 text-white px-3 py-1 rounded-full text-xs font-semibold'>
                        {story.achievement}
                      </div>
                    </div>

                    <div className='mb-4'>
                      <span className='text-navy-600 dark:text-gold-400 font-semibold text-sm'>
                        Program: {story.program}
                      </span>
                    </div>

                    <p className='text-gray-700 dark:text-gray-300 leading-relaxed text-sm'>
                      {story.story}
                    </p>
                  </div>
                </div>
              </AnimatedSection>
            ))}
          </div>
        </div>
      </section>

      {/* Yearly Impact Chart */}
      <section className='py-20 bg-white dark:bg-gray-900'>
        <div className='container mx-auto px-6'>
          <AnimatedSection>
            <div className='text-center mb-16'>
              <h2 className='text-3xl md:text-4xl font-bold text-navy-800 dark:text-white mb-6'>
                Growth Over Time
              </h2>
              <p className='text-lg text-gray-700 dark:text-gray-300 max-w-2xl mx-auto'>
                Our expanding impact year by year, reaching more women and
                creating more opportunities.
              </p>
            </div>
          </AnimatedSection>

          <div className='bg-gradient-to-br from-navy-50 to-gold-50 dark:from-gray-800 dark:to-gray-700 p-8 rounded-2xl'>
            <div className='grid md:grid-cols-5 gap-6'>
              {yearlyImpact.map((year, index) => (
                <AnimatedSection key={index} delay={index * 200}>
                  <div className='text-center'>
                    <div className='text-2xl font-bold text-navy-800 dark:text-white mb-4'>
                      {year.year}
                    </div>
                    <div className='space-y-3'>
                      <div className='bg-white dark:bg-gray-800 p-3 rounded-xl'>
                        <div className='text-lg font-bold text-blue-600 dark:text-blue-400'>
                          {year.women}
                        </div>
                        <div className='text-xs text-gray-600 dark:text-gray-400'>
                          Women Trained
                        </div>
                      </div>
                      <div className='bg-white dark:bg-gray-800 p-3 rounded-xl'>
                        <div className='text-lg font-bold text-green-600 dark:text-green-400'>
                          {year.businesses}
                        </div>
                        <div className='text-xs text-gray-600 dark:text-gray-400'>
                          Businesses Started
                        </div>
                      </div>
                      <div className='bg-white dark:bg-gray-800 p-3 rounded-xl'>
                        <div className='text-lg font-bold text-purple-600 dark:text-purple-400'>
                          {year.employment}
                        </div>
                        <div className='text-xs text-gray-600 dark:text-gray-400'>
                          Jobs Created
                        </div>
                      </div>
                    </div>
                  </div>
                </AnimatedSection>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* SDG Alignment */}
      <section className='py-20 bg-gradient-to-br from-navy-50 via-white to-gold-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900'>
        <div className='container mx-auto px-6'>
          <AnimatedSection>
            <div className='text-center mb-16'>
              <h2 className='text-3xl md:text-4xl font-bold text-navy-800 dark:text-white mb-6'>
                UN Sustainable Development Goals
              </h2>
              <p className='text-lg text-gray-700 dark:text-gray-300 max-w-2xl mx-auto'>
                Our work directly contributes to achieving the United Nations
                Sustainable Development Goals.
              </p>
            </div>
          </AnimatedSection>

          <div className='grid md:grid-cols-2 lg:grid-cols-5 gap-6'>
            {sdgGoals.map((goal, index) => (
              <AnimatedSection key={index} delay={index * 200}>
                <div className='bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group hover:scale-105 text-center'>
                  <div className='w-16 h-16 bg-gradient-to-r from-navy-600 to-gold-600 rounded-full flex items-center justify-center mx-auto mb-4 text-white font-bold text-xl group-hover:scale-110 transition-transform duration-300'>
                    {goal.number}
                  </div>
                  <h3 className='text-lg font-bold text-navy-800 dark:text-white mb-3'>
                    {goal.title}
                  </h3>
                  <p className='text-gray-700 dark:text-gray-300 text-sm leading-relaxed'>
                    {goal.description}
                  </p>
                </div>
              </AnimatedSection>
            ))}
          </div>
        </div>
      </section>
      <GallerySection />
      {/* Call to Action */}
      <section className='py-20 bg-gradient-to-r from-navy-800 to-navy-600 text-white'>
        <div className='container mx-auto px-6'>
          <AnimatedSection>
            <div className='text-center'>
              <h2 className='text-3xl md:text-4xl font-bold mb-6'>
                Be Part of Our Impact
              </h2>
              <p className='text-xl mb-8 max-w-2xl mx-auto opacity-90'>
                Join us in creating lasting change. Your support helps us reach
                more women and build stronger communities.
              </p>
              <div className='flex flex-col sm:flex-row gap-4 justify-center'>
                <button className='bg-gradient-to-r from-gold-500 to-gold-400 text-navy-800 px-8 py-4 rounded-xl font-bold text-lg hover:from-gold-400 hover:to-gold-300 transition-all duration-300 transform hover:scale-105'>
                  Support Our Mission
                </button>
                <button className='border-2 border-white text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-white hover:text-navy-800 transition-all duration-300'>
                  Volunteer With Us
                </button>
              </div>
            </div>
          </AnimatedSection>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Impact;
