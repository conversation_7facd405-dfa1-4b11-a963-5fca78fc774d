import React from 'react';
import { Award, Calendar, MapPin } from 'lucide-react';

const SuccessStories = () => {
  const successStories = [
    {
      name: '<PERSON><PERSON>',
      age: 32,
      program: 'Tailoring & Fashion Design',
      story: 'After completing our 6-month tailoring program, <PERSON><PERSON> started her own clothing business. She now employs 3 other women and supports her family of 5.',
      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=center',
      achievement: 'Business Owner',
      year: '2023',
      location: '<PERSON><PERSON><PERSON>'
    },
    {
      name: '<PERSON>',
      age: 28,
      program: 'Digital Literacy & Computer Skills',
      story: '<PERSON> learned computer skills and now works as a data entry clerk for a local NGO. She\'s also teaching other women basic computer skills.',
      image: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=300&h=300&fit=crop&crop=center',
      achievement: 'IT Professional',
      year: '2023',
      location: 'Nairobi'
    },
    {
      name: '<PERSON>',
      age: 35,
      program: 'Culinary Arts & Catering',
      story: '<PERSON> transformed her passion for cooking into a thriving catering business. She now provides meals for local events and has trained 5 apprentices.',
      image: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=300&h=300&fit=crop&crop=center',
      achievement: 'Chef & Entrepreneur',
      year: '2022',
      location: 'Mombasa'
    }
  ];

  return (
    <section className='py-20 bg-gradient-to-br from-gold-50 to-navy-50 dark:from-gray-800 dark:to-gray-900'>
      <div className='container mx-auto px-6'>
        <div className='text-center mb-16'>
          <h2 className='text-3xl md:text-4xl font-bold text-navy-800 dark:text-white mb-6'>
            Success Stories
          </h2>
          <p className='text-lg text-gray-700 dark:text-gray-300 max-w-2xl mx-auto'>
            Real women, real transformations, real impact on communities.
          </p>
        </div>

        <div className='grid lg:grid-cols-3 gap-8'>
          {successStories.map((story, index) => (
            <div key={index} className='bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 group hover:scale-105'>
              <div className='aspect-square overflow-hidden'>
                <img
                  src={story.image}
                  alt={story.name}
                  className='w-full h-full object-cover group-hover:scale-110 transition-transform duration-500'
                />
              </div>
              <div className='p-6'>
                <div className='flex items-center justify-between mb-4'>
                  <div>
                    <h3 className='text-xl font-bold text-navy-800 dark:text-white'>
                      {story.name}
                    </h3>
                    <div className='flex items-center space-x-4 text-gray-600 dark:text-gray-400 text-sm mt-1'>
                      <span>Age {story.age}</span>
                      <div className='flex items-center space-x-1'>
                        <Calendar className='w-3 h-3' />
                        <span>{story.year}</span>
                      </div>
                      <div className='flex items-center space-x-1'>
                        <MapPin className='w-3 h-3' />
                        <span>{story.location}</span>
                      </div>
                    </div>
                  </div>
                  <div className='bg-gradient-to-r from-gold-500 to-gold-600 text-white px-3 py-1 rounded-full text-xs font-semibold'>
                    {story.achievement}
                  </div>
                </div>
                
                <div className='mb-4'>
                  <span className='text-navy-600 dark:text-gold-400 font-semibold text-sm'>
                    Program: {story.program}
                  </span>
                </div>
                
                <p className='text-gray-700 dark:text-gray-300 leading-relaxed text-sm'>
                  {story.story}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default SuccessStories;