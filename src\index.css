
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Enhanced color system */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.75rem;

    /* Enhanced navy and gold color system */
    --brand-navy: 220 30% 15%;
    --brand-navy-light: 220 25% 25%;
    --brand-navy-dark: 220 35% 10%;
    --brand-gold: 45 95% 55%;
    --brand-gold-light: 45 100% 65%;
    --brand-gold-dark: 45 90% 45%;
    --brand-cream: 45 40% 96%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    scroll-behavior: smooth;
    line-height: 1.6;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    line-height: 1.2;
  }
}

/* Sophisticated animations */
@keyframes hero-fade-in {
  0% {
    opacity: 0;
    transform: translateY(60px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-up {
  0% {
    opacity: 0;
    transform: translateY(40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float-gentle {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes glow-elegant {
  0%, 100% {
    box-shadow: 0 0 30px rgba(251, 191, 36, 0.3);
  }
  50% {
    box-shadow: 0 0 50px rgba(251, 191, 36, 0.5);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes morph {
  0%, 100% {
    border-radius: 40% 60% 70% 30% / 40% 50% 60% 50%;
  }
  50% {
    border-radius: 70% 30% 50% 50% / 60% 40% 40% 60%;
  }
}

/* Utility classes */
.animate-hero-fade-in {
  animation: hero-fade-in 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-slide-up {
  animation: slide-up 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-float-gentle {
  animation: float-gentle 6s ease-in-out infinite;
}

.animate-glow-elegant {
  animation: glow-elegant 4s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-morph {
  animation: morph 8s ease-in-out infinite;
}

/* Enhanced gradients */
.gradient-hero {
  background: linear-gradient(135deg, 
    hsl(var(--brand-cream)) 0%,
    rgba(45, 95, 155, 0.1) 25%,
    rgba(251, 191, 36, 0.1) 50%,
    hsl(var(--brand-cream)) 100%);
}

.gradient-text-hero {
  background: linear-gradient(135deg, 
    hsl(var(--brand-navy)),
    hsl(var(--brand-gold)),
    hsl(var(--brand-navy-light)));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 100%;
  animation: shimmer 3s linear infinite;
}

.gradient-button {
  background: linear-gradient(135deg, 
    hsl(var(--brand-navy)),
    hsl(var(--brand-navy-light)));
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.gradient-button:hover {
  background: linear-gradient(135deg, 
    hsl(var(--brand-navy-light)),
    hsl(var(--brand-navy-dark)));
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(45, 95, 155, 0.3);
}

/* Sophisticated card styles */
.card-elegant {
  @apply bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-white/30;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-elegant:hover {
  @apply shadow-2xl;
  transform: translateY(-8px) scale(1.02);
  border-color: rgba(251, 191, 36, 0.3);
}

.dark .card-elegant {
  @apply bg-gray-900/90 border-gray-800/50;
}

.dark .card-elegant:hover {
  border-color: rgba(251, 191, 36, 0.3);
}

/* Morphing blob backgrounds */
.blob-bg {
  background: linear-gradient(135deg, 
    rgba(251, 191, 36, 0.2),
    rgba(45, 95, 155, 0.15));
  filter: blur(40px);
}

/* Enhanced section styling */
.section-hero {
  background: linear-gradient(135deg, 
    rgba(248, 250, 252, 1) 0%,
    rgba(251, 191, 36, 0.1) 50%,
    rgba(248, 250, 252, 1) 100%);
  position: relative;
  overflow: hidden;
}

.section-hero::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, 
    rgba(251, 191, 36, 0.1) 0%, 
    transparent 70%);
  animation: morph 15s ease-in-out infinite;
}

/* Typography enhancements */
.text-hero {
  font-family: 'Playfair Display', serif;
  font-weight: 700;
  line-height: 0.9;
  letter-spacing: -0.02em;
}

.text-elegant {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  line-height: 1.7;
  letter-spacing: 0.01em;
}

/* Refined hover states */
.hover-elegant {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-elegant:hover {
  transform: translateY(-4px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* Responsive typography */
@media (max-width: 768px) {
  .text-hero {
    line-height: 1.1;
  }
}

/* Performance optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, 
    hsl(var(--brand-gold)), 
    hsl(var(--brand-navy)));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, 
    hsl(var(--brand-gold-light)), 
    hsl(var(--brand-navy-light)));
}
