import React from 'react';
import { ExternalLink, Users, Globe, Building } from 'lucide-react';
import AnimatedSection from './AnimatedSection';
import { Link } from 'react-router-dom';
import {
  partners,
  featuredPartners,
  partnerCategories,
} from '@/data/partnersData';

interface PartnersSectionProps {
  title?: string;
  subtitle?: string;
  description?: string;
  showHeader?: boolean;
  variant?: 'featured' | 'categories' | 'grid' | 'logos';
  maxPartners?: number;
  backgroundColor?: string;
  showCTA?: boolean;
}

const PartnersSection: React.FC<PartnersSectionProps> = ({
  title = 'Our Partners',
  subtitle = 'Building Stronger Communities Together',
  description = 'We collaborate with leading organizations to maximize our impact and create lasting change in communities.',
  showHeader = true,
  variant = 'featured',
  maxPartners,
  backgroundColor = 'bg-gradient-to-br from-navy-50 via-white to-gold-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900',
  showCTA = true,
}) => {
  const displayPartners = maxPartners
    ? partners.slice(0, maxPartners)
    : partners;
  const displayFeatured = maxPartners
    ? featuredPartners.slice(0, maxPartners)
    : featuredPartners;

  const getPartnerIcon = (type: string) => {
    switch (type) {
      case 'international':
        return <Globe className='w-5 h-5' />;
      case 'government':
        return <Building className='w-5 h-5' />;
      default:
        return <Users className='w-5 h-5' />;
    }
  };

  return (
    <section className={`py-20 ${backgroundColor}`}>
      <div className='container mx-auto px-4 lg:px-8 max-w-7xl'>
        {/* Header */}
        {showHeader && (
          <AnimatedSection>
            <div className='text-center mb-16'>
              <h2 className='text-3xl md:text-4xl lg:text-5xl font-bold text-navy-800 dark:text-white mb-6'>
                {title}
              </h2>
              <div className='w-24 h-1 bg-gradient-to-r from-gold-500 to-navy-600 mx-auto mb-6 rounded-full'></div>
              <p className='text-xl text-gray-700 dark:text-gray-300 max-w-4xl mx-auto leading-relaxed'>
                {description}
              </p>
            </div>
          </AnimatedSection>
        )}

        {/* Featured Partners */}
        {variant === 'featured' && (
          <div className='grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16'>
            {displayFeatured.map((partner, index) => (
              <AnimatedSection key={partner.id} delay={index * 200}>
                <div className='group bg-white dark:bg-gray-800 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden border border-gray-100 dark:border-gray-700'>
                  {/* Partner Logo */}
                  <div className='relative h-32 bg-gradient-to-br from-gray-50 to-white dark:from-gray-700 dark:to-gray-800 flex items-center justify-center p-6'>
                    <img
                      src={partner.logo}
                      alt={partner.name}
                      className='max-h-16 max-w-full object-contain group-hover:scale-110 transition-transform duration-300'
                    />
                    <div className='absolute top-3 right-3'>
                      {getPartnerIcon(partner.type)}
                    </div>
                  </div>

                  {/* Partner Info */}
                  <div className='p-6'>
                    <h3 className='text-lg font-bold text-navy-800 dark:text-white mb-2'>
                      {partner.name}
                    </h3>
                    <p className='text-sm text-gold-600 dark:text-gold-400 font-semibold mb-3 capitalize'>
                      {partner.type} Partner
                    </p>
                    <p className='text-gray-700 dark:text-gray-300 text-sm mb-4 leading-relaxed line-clamp-3'>
                      {partner.description}
                    </p>

                    {/* Partnership Info */}
                    <div className='space-y-2 mb-4'>
                      <div className='flex items-center text-xs text-gray-600 dark:text-gray-400'>
                        <span className='font-semibold mr-2'>Since:</span>
                        <span>{partner.partnership.since}</span>
                      </div>
                      <div className='flex items-center text-xs text-gray-600 dark:text-gray-400'>
                        <span className='font-semibold mr-2'>Focus:</span>
                        <span className='line-clamp-1'>
                          {partner.partnership.focus.slice(0, 2).join(', ')}
                        </span>
                      </div>
                    </div>

                    {/* Website Link */}
                    {partner.website && (
                      <a
                        href={partner.website}
                        target='_blank'
                        rel='noopener noreferrer'
                        className='group/link inline-flex items-center text-navy-800 dark:text-navy-400 font-semibold text-sm hover:text-gold-600 dark:hover:text-gold-400 transition-colors duration-300'
                      >
                        <span>Visit Website</span>
                        <ExternalLink className='w-3 h-3 ml-1 group-hover/link:translate-x-0.5 group-hover/link:-translate-y-0.5 transition-transform duration-300' />
                      </a>
                    )}
                  </div>
                </div>
              </AnimatedSection>
            ))}
          </div>
        )}

        {/* Partner Categories */}
        {variant === 'categories' && (
          <div className='grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16'>
            {partnerCategories.map((category, index) => (
              <AnimatedSection key={category.id} delay={index * 200}>
                <div className='bg-white dark:bg-gray-800 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 p-8 border border-gray-100 dark:border-gray-700'>
                  <div className='text-center mb-6'>
                    <div className='text-4xl mb-4'>{category.icon}</div>
                    <h3 className='text-xl font-bold text-navy-800 dark:text-white mb-3'>
                      {category.name}
                    </h3>
                    <p className='text-gray-700 dark:text-gray-300 text-sm leading-relaxed mb-4'>
                      {category.description}
                    </p>
                    <div className='text-2xl font-bold text-gold-600 dark:text-gold-400'>
                      {category.partners.length}
                    </div>
                    <div className='text-xs text-gray-600 dark:text-gray-400 uppercase tracking-wider'>
                      Partners
                    </div>
                  </div>
                </div>
              </AnimatedSection>
            ))}
          </div>
        )}

        {/* Logo Grid */}
        {variant === 'logos' && (
          <div className='grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 mb-16'>
            {displayPartners.map((partner, index) => (
              <AnimatedSection key={partner.id} delay={index * 100}>
                <div className='group bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 p-6 border border-gray-100 dark:border-gray-700'>
                  <img
                    src={partner.logo}
                    alt={partner.name}
                    className='w-full h-16 object-contain group-hover:scale-110 transition-transform duration-300'
                  />
                </div>
              </AnimatedSection>
            ))}
          </div>
        )}

        {/* Call to Action */}
        {showCTA && (
          <AnimatedSection delay={800}>
            <div className='text-center'>
              <Link
                to='/partners'
                className='group inline-flex items-center space-x-3 bg-gradient-to-r from-navy-800 to-navy-600 text-white px-10 py-5 rounded-2xl font-semibold text-lg hover:from-navy-700 hover:to-navy-500 transition-all duration-300 transform hover:scale-105 shadow-2xl hover:shadow-3xl'
              >
                <span>View All Partners</span>
                <div className='w-0 group-hover:w-6 h-0.5 bg-white transition-all duration-300'></div>
                <ExternalLink className='w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-300' />
              </Link>
            </div>
          </AnimatedSection>
        )}
      </div>
    </section>
  );
};

export default React.memo(PartnersSection);
