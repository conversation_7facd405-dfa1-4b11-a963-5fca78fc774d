import React from 'react';
import { Star, ShoppingBag, Eye, Heart, ShoppingCart } from 'lucide-react';
import AnimatedSection from './AnimatedSection';
import CustomButton from './CustomButton';
import { featuredProducts } from '@/data/productsData';

interface HandpickedSelectionProps {
  title?: string;
  subtitle?: string;
  description?: string;
  showHeader?: boolean;
  maxProducts?: number;
  backgroundColor?: string;
  showCTA?: boolean;
}

const HandpickedSelection: React.FC<HandpickedSelectionProps> = ({
  title = 'Handpicked Selection',
  subtitle = 'Featured Products',
  description = 'Featured products from our collection',
  showHeader = true,
  maxProducts = 4,
  backgroundColor = 'bg-gradient-to-br from-slate-50 via-white to-blue-50/30 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900',
  showCTA = true,
}) => {
  const displayProducts = maxProducts
    ? featuredProducts.slice(0, maxProducts)
    : featuredProducts;

  return (
    <section className={`section-padding ${backgroundColor}`}>
      <div className='content-max-width container-padding'>
        {/* Header */}
        {showHeader && (
          <AnimatedSection>
            <div className='text-center mb-16'>
              <div className='mb-6'>
                <span className='inline-block bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-4 py-2 rounded-full text-sm font-semibold tracking-wide uppercase'>
                  {subtitle}
                </span>
              </div>
              <h2 className='heading-lg text-slate-800 dark:text-white mb-6 leading-none'>
                {title}
              </h2>
              <div className='w-32 h-2 bg-gradient-to-r from-blue-500 via-sky-400 to-indigo-500 mx-auto mb-8 rounded-full shadow-lg'></div>
              <p className='text-xl lg:text-2xl text-slate-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed font-light'>
                {description}
              </p>
            </div>
          </AnimatedSection>
        )}

        {/* Products Grid - Improved Layout */}
        <div className='grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-16'>
          {displayProducts.map((product, index) => (
            <AnimatedSection key={product.id} delay={index * 200}>
              <div className='group relative bg-white dark:bg-slate-800 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 hover:scale-[1.02] overflow-hidden border border-slate-200/50 dark:border-slate-700/50 h-full flex flex-col'>
                {/* Product Image - Fixed Height */}
                <div className='relative h-56 overflow-hidden flex-shrink-0'>
                  <img
                    src={product.image}
                    alt={product.name}
                    className='w-full h-full object-cover group-hover:scale-110 transition-transform duration-700 brightness-105 contrast-110 saturate-110'
                  />
                  {/* Subtle gradient overlay for better text readability */}
                  <div className='absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent'></div>

                  {/* Colorful accent border */}
                  <div className='absolute inset-0 border-2 border-transparent group-hover:border-blue-400/50 transition-all duration-300'></div>

                  {/* Vibrant availability badge */}
                  <div className='absolute top-4 left-4'>
                    <span
                      className={`px-4 py-2 rounded-full text-xs font-bold shadow-lg backdrop-blur-sm ${
                        product.availability === 'available'
                          ? 'bg-gradient-to-r from-emerald-500 to-green-500 text-white'
                          : product.availability === 'limited'
                          ? 'bg-gradient-to-r from-amber-500 to-orange-500 text-white'
                          : 'bg-gradient-to-r from-red-500 to-pink-500 text-white'
                      }`}
                    >
                      {product.availability === 'available'
                        ? '✓ Available'
                        : product.availability === 'limited'
                        ? '⚡ Limited'
                        : '✕ Sold Out'}
                    </span>
                  </div>

                  {/* Hover actions */}
                  <div className='absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center'>
                    <div className='flex space-x-3'>
                      <button className='bg-white/90 hover:bg-white text-slate-800 p-3 rounded-full transition-all duration-300 transform hover:scale-110 shadow-lg'>
                        <Eye className='w-5 h-5' />
                      </button>
                      <button className='bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full transition-all duration-300 transform hover:scale-110 shadow-lg'>
                        <ShoppingBag className='w-5 h-5' />
                      </button>
                      {/* <button className='bg-red-500 hover:bg-red-600 text-white p-3 rounded-full transition-all duration-300 transform hover:scale-110 shadow-lg'>
                        <Heart className='w-5 h-5' />
                      </button> */}
                    </div>
                  </div>
                </div>
                {/* Product Info - Flexible Layout */}
                <div className='p-5 flex-1 flex flex-col'>
                  {/* Compact Category */}
                  <div className='mb-2'>
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-semibold uppercase tracking-wide ${
                        product.category === 'arts-crafts'
                          ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
                          : product.category === 'fashion'
                          ? 'bg-gradient-to-r from-indigo-500 to-blue-500 text-white'
                          : product.category === 'beauty'
                          ? 'bg-gradient-to-r from-rose-500 to-pink-500 text-white'
                          : 'bg-gradient-to-r from-teal-500 to-cyan-500 text-white'
                      }`}
                    >
                      {product.category.replace('-', ' & ')}
                    </span>
                  </div>

                  {/* Product Name */}
                  <h3 className='text-lg font-bold text-slate-800 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300 line-clamp-2'>
                    {product.name}
                  </h3>

                  {/* Description */}
                  <p className='text-slate-600 dark:text-gray-300 text-sm leading-relaxed mb-3 line-clamp-2 flex-1'>
                    {product.description}
                  </p>
                  {/* Compact Features */}
                  <div className='flex flex-wrap gap-1 mb-3'>
                    {product.features
                      ?.slice(0, 2)
                      .map((feature, featureIndex) => (
                        <span
                          key={featureIndex}
                          className={`text-xs font-medium px-2 py-0.5 rounded-full ${
                            featureIndex === 0
                              ? 'bg-gradient-to-r from-cyan-400 to-blue-400 text-white'
                              : 'bg-gradient-to-r from-violet-400 to-purple-400 text-white'
                          }`}
                        >
                          {feature}
                        </span>
                      ))}
                  </div>
                  <div className='border-t border-gray-200 dark:border-gray-700 pt-3 mb-2 mt-2'></div>
                  {/* Bottom Section - Compact Layout */}
                  <div className='mt-auto pt-3 border-t border-slate-200/50 dark:border-slate-600/50'>
                    {/* Price and Artisan */}
                    <div className='flex justify-between items-center mb-2'>
                      <div>
                        <span className='text-lg font-bold text-slate-800 dark:text-white'>
                          {product.price}
                        </span>
                      </div>
                      {/* <div className='text-right'>
                        <p className='text-xs text-slate-500 dark:text-gray-400 truncate max-w-20'>
                          by {product.artisan.split(' ')[0]}
                        </p>
                      </div> */}
                    </div>

                    {/* Rating and Cart */}
                    <div className='flex items-center justify-between'>
                      <div className='flex items-center space-x-1'>
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-3 h-3 ${
                              i < 4
                                ? 'text-yellow-400 fill-current'
                                : 'text-gray-300'
                            }`}
                          />
                        ))}
                        <span className='text-xs text-slate-500 dark:text-gray-400 ml-1'>
                          4.8
                        </span>
                      </div>
                      <button className='bg-gradient-to-r from-blue-600 to-blue-700 text-white px-3 py-1.5 rounded-lg text-xs font-semibold hover:from-blue-700 hover:to-blue-800 transition-all duration-300 flex items-center space-x-1'>
                        {/* <ShoppingCart className='w-3 h-3' /> */}
                        <ShoppingCart className='w-4 h-4' />
                        <span>Add to Cart</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </AnimatedSection>
          ))}
        </div>

        {/* Call to Action */}
        {showCTA && (
          <AnimatedSection delay={800}>
            <div className='text-center'>
              <CustomButton
                to='/products'
                variant='primary'
                size='lg'
                icon={ShoppingBag}
                iconPosition='left'
              >
                View All Products
              </CustomButton>
              <p className='mt-6 text-slate-500 dark:text-gray-400 text-sm'>
                Discover more handcrafted products from our talented artisans
              </p>
            </div>
          </AnimatedSection>
        )}
      </div>
    </section>
  );
};

export default React.memo(HandpickedSelection);
