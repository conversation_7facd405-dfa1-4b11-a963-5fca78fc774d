import React from 'react';
import { <PERSON>, ShoppingBag, <PERSON>, Heart } from 'lucide-react';
import AnimatedSection from './AnimatedSection';
import { Link } from 'react-router-dom';
import { featuredProducts } from '@/data/productsData';

interface HandpickedSelectionProps {
  title?: string;
  subtitle?: string;
  description?: string;
  showHeader?: boolean;
  maxProducts?: number;
  backgroundColor?: string;
  showCTA?: boolean;
}

const HandpickedSelection: React.FC<HandpickedSelectionProps> = ({
  title = 'Handpicked Selection',
  subtitle = 'Featured Products',
  description = 'Featured products from our collection',
  showHeader = true,
  maxProducts = 4,
  backgroundColor = 'bg-gradient-to-br from-slate-50 via-white to-blue-50/30 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900',
  showCTA = true,
}) => {
  const displayProducts = maxProducts ? featuredProducts.slice(0, maxProducts) : featuredProducts;

  return (
    <section className={`py-24 ${backgroundColor}`}>
      <div className='container mx-auto px-4 lg:px-8 max-w-7xl'>
        {/* Header */}
        {showHeader && (
          <AnimatedSection>
            <div className='text-center mb-16'>
              <div className='mb-6'>
                <span className='inline-block bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-4 py-2 rounded-full text-sm font-semibold tracking-wide uppercase'>
                  {subtitle}
                </span>
              </div>
              <h2 className='text-4xl md:text-5xl lg:text-6xl font-black text-slate-800 dark:text-white mb-6 tracking-tight leading-none'>
                {title}
              </h2>
              <div className='w-32 h-2 bg-gradient-to-r from-blue-500 via-sky-400 to-indigo-500 mx-auto mb-8 rounded-full shadow-lg'></div>
              <p className='text-xl lg:text-2xl text-slate-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed font-light'>
                {description}
              </p>
            </div>
          </AnimatedSection>
        )}

        {/* Products Grid */}
        <div className='grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16'>
          {displayProducts.map((product, index) => (
            <AnimatedSection key={product.id} delay={index * 200}>
              <div className='group relative bg-white dark:bg-slate-800 rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-700 transform hover:-translate-y-4 hover:scale-105 overflow-hidden border border-slate-200/50 dark:border-slate-700/50'>
                {/* Product Image */}
                <div className='relative h-64 overflow-hidden'>
                  <img
                    src={product.image}
                    alt={product.name}
                    className='w-full h-full object-cover group-hover:scale-110 transition-transform duration-700'
                  />
                  {/* Gradient overlay */}
                  <div className='absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent'></div>
                  
                  {/* Availability badge */}
                  <div className='absolute top-4 left-4'>
                    <span className={`px-3 py-1 rounded-full text-xs font-bold ${
                      product.availability === 'available' 
                        ? 'bg-green-500 text-white' 
                        : product.availability === 'limited'
                        ? 'bg-amber-500 text-white'
                        : 'bg-red-500 text-white'
                    }`}>
                      {product.availability === 'available' ? 'Available' : 
                       product.availability === 'limited' ? 'Limited' : 'Sold Out'}
                    </span>
                  </div>

                  {/* Hover actions */}
                  <div className='absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center'>
                    <div className='flex space-x-3'>
                      <button className='bg-white/90 hover:bg-white text-slate-800 p-3 rounded-full transition-all duration-300 transform hover:scale-110 shadow-lg'>
                        <Eye className='w-5 h-5' />
                      </button>
                      <button className='bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full transition-all duration-300 transform hover:scale-110 shadow-lg'>
                        <ShoppingBag className='w-5 h-5' />
                      </button>
                      <button className='bg-red-500 hover:bg-red-600 text-white p-3 rounded-full transition-all duration-300 transform hover:scale-110 shadow-lg'>
                        <Heart className='w-5 h-5' />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Product Info */}
                <div className='p-6'>
                  {/* Category */}
                  <div className='mb-3'>
                    <span className='text-xs font-semibold text-blue-600 dark:text-blue-400 uppercase tracking-wider'>
                      {product.category.replace('-', ' & ')}
                    </span>
                  </div>

                  {/* Product Name */}
                  <h3 className='text-xl font-bold text-slate-800 dark:text-white mb-3 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300'>
                    {product.name}
                  </h3>

                  {/* Description */}
                  <p className='text-slate-600 dark:text-gray-300 text-sm leading-relaxed mb-4 line-clamp-2'>
                    {product.description}
                  </p>

                  {/* Price and Artisan */}
                  <div className='flex justify-between items-center mb-4'>
                    <div>
                      <span className='text-lg font-bold text-slate-800 dark:text-white'>
                        {product.price}
                      </span>
                    </div>
                    <div className='text-right'>
                      <p className='text-xs text-slate-500 dark:text-gray-400'>
                        by {product.artisan}
                      </p>
                    </div>
                  </div>

                  {/* Features */}
                  <div className='flex flex-wrap gap-1 mb-4'>
                    {product.features?.slice(0, 2).map((feature, featureIndex) => (
                      <span
                        key={featureIndex}
                        className='text-xs bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-gray-300 px-2 py-1 rounded-full'
                      >
                        {feature}
                      </span>
                    ))}
                  </div>

                  {/* Rating */}
                  <div className='flex items-center justify-between'>
                    <div className='flex items-center space-x-1'>
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`w-4 h-4 ${
                            i < 4 ? 'text-yellow-400 fill-current' : 'text-gray-300'
                          }`}
                        />
                      ))}
                      <span className='text-xs text-slate-500 dark:text-gray-400 ml-2'>
                        (4.8)
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </AnimatedSection>
          ))}
        </div>

        {/* Call to Action */}
        {showCTA && (
          <AnimatedSection delay={800}>
            <div className='text-center'>
              <Link
                to='/products'
                className='group relative inline-flex items-center space-x-4 bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 text-white px-12 py-6 rounded-2xl font-bold text-xl hover:from-blue-500 hover:via-blue-600 hover:to-indigo-600 transition-all duration-500 transform hover:scale-110 hover:-translate-y-2 shadow-2xl hover:shadow-blue-500/25 overflow-hidden'
              >
                <div className='absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500'></div>
                <ShoppingBag className='relative w-6 h-6' />
                <span className='relative tracking-wide'>View All Products</span>
                <div className='relative w-0 group-hover:w-8 h-0.5 bg-white transition-all duration-500'></div>
                <span className='relative transform group-hover:translate-x-2 transition-transform duration-500 text-2xl'>
                  →
                </span>
              </Link>
              <p className='mt-6 text-slate-500 dark:text-gray-400 text-sm'>
                Discover more handcrafted products from our talented artisans
              </p>
            </div>
          </AnimatedSection>
        )}
      </div>
    </section>
  );
};

export default React.memo(HandpickedSelection);
