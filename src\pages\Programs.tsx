import React from 'react';
import {
  GraduationCap,
  Heart,
  Users,
  Scissors,
  Utensils,
  Laptop,
  DollarSign,
  Languages,
  Palette,
} from 'lucide-react';
import AnimatedSection from '@/components/AnimatedSection';
import PageHero from '@/components/PageHero';
import GallerySection from '@/components/GallerySection';
import Footer from '@/components/Footer';
import { images } from '@/utils/images';

const Programs = () => {
  const mainPrograms = [
    {
      title: 'Serenity Alliance',
      subtitle: 'Building Peace Together',
      description:
        'Promoting collaboration among women from diverse backgrounds, fostering unity and understanding through cultural exchange and peacebuilding initiatives.',
      features: [
        'Peace Building Capacity',
        'Cultural Exchange',
        'Community Solidarity',
        'Conflict Resolution',
      ],
      color: 'from-blue-500 to-blue-600',
      icon: '🕊️',
      image: images.community.events.gathering,
    },
    {
      title: 'Skills Development',
      subtitle: 'Empowering Through Education',
      description:
        'Equipping women with practical skills to foster economic independence and self-reliance through comprehensive training programs.',
      features: [
        'Hands-on Training',
        'Financial Literacy',
        'Entrepreneurship Support',
        'Certification Programs',
      ],
      color: 'from-gold-500 to-gold-600',
      icon: '🎓',
      image: images.programs.training.general1,
    },
    {
      title: 'Community Health',
      subtitle: 'Healing Hearts & Minds',
      description:
        'Providing counseling services and trauma recovery programs for women facing emotional and psychological challenges.',
      features: [
        'Trauma Counseling',
        'SGBV Support',
        'Emotional Healing',
        'Mental Health Awareness',
      ],
      color: 'from-green-500 to-green-600',
      icon: '💚',
      image: images.programs.counseling.group,
    },
  ];

  const skillsPrograms = [
    {
      name: 'Sewing & Tailoring',
      icon: Scissors,
      description: 'Professional garment making and fashion design skills',
      duration: '6 months',
      level: 'Beginner to Advanced',
    },
    {
      name: 'Culinary Arts',
      icon: Utensils,
      description: 'Cooking, baking, and food service management',
      duration: '4 months',
      level: 'All levels',
    },
    {
      name: 'Digital Literacy',
      icon: Laptop,
      description: 'Computer skills and digital communication',
      duration: '3 months',
      level: 'Beginner',
    },
    {
      name: 'Financial Management',
      icon: DollarSign,
      description: 'Budgeting, savings, and business planning',
      duration: '2 months',
      level: 'All levels',
    },
    {
      name: 'Language Skills',
      icon: Languages,
      description: 'English and Swahili communication skills',
      duration: '6 months',
      level: 'Beginner to Intermediate',
    },
    {
      name: 'Arts & Crafts',
      icon: Palette,
      description: 'Traditional crafts and modern art techniques',
      duration: '4 months',
      level: 'All levels',
    },
  ];

  return (
    <div className='min-h-screen pt-20'>
      {/* Hero Section with Background Image */}
      <PageHero
        title='Our Programs'
        subtitle='Empowerment Through Education'
        description='Comprehensive programs designed to empower women through education, skills development, and community support.'
        backgroundImage={images.programs.training.general2}
        height='sm'
        overlay='navy'
        scrollToId='main-programs'
      />

      {/* Main Programs */}
      <section id='main-programs' className='py-20 bg-white dark:bg-gray-900'>
        <div className='container mx-auto px-6'>
          <AnimatedSection>
            <div className='text-center mb-16'>
              <h2 className='text-3xl md:text-4xl font-bold text-navy-800 dark:text-white mb-6'>
                Core Programs
              </h2>
              <p className='text-lg text-gray-700 dark:text-gray-300 max-w-2xl mx-auto'>
                Three foundational pillars that form the backbone of our mission
                to empower women and build peace.
              </p>
            </div>
          </AnimatedSection>

          <div className='space-y-16'>
            {mainPrograms.map((program, index) => (
              <AnimatedSection key={index} delay={index * 300}>
                <div
                  className={`grid lg:grid-cols-2 gap-12 items-center ${
                    index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''
                  }`}
                >
                  <div className={index % 2 === 1 ? 'lg:col-start-2' : ''}>
                    <div className='space-y-6'>
                      <div className='flex items-center space-x-4'>
                        <div
                          className={`w-16 h-16 bg-gradient-to-r ${program.color} rounded-2xl flex items-center justify-center text-2xl shadow-lg`}
                        >
                          {program.icon}
                        </div>
                        <div>
                          <h3 className='text-2xl md:text-3xl font-bold text-navy-800 dark:text-white'>
                            {program.title}
                          </h3>
                          <p className='text-gold-600 dark:text-gold-400 font-semibold'>
                            {program.subtitle}
                          </p>
                        </div>
                      </div>

                      <p className='text-gray-700 dark:text-gray-300 text-lg leading-relaxed'>
                        {program.description}
                      </p>

                      <div className='grid grid-cols-2 gap-4'>
                        {program.features.map((feature, idx) => (
                          <div
                            key={idx}
                            className='flex items-center space-x-2'
                          >
                            <div
                              className={`w-2 h-2 bg-gradient-to-r ${program.color} rounded-full`}
                            ></div>
                            <span className='text-gray-700 dark:text-gray-300 text-sm'>
                              {feature}
                            </span>
                          </div>
                        ))}
                      </div>

                      <button
                        className={`bg-gradient-to-r ${program.color} text-white px-8 py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105`}
                      >
                        Learn More
                      </button>
                    </div>
                  </div>

                  <div className={index % 2 === 1 ? 'lg:col-start-1' : ''}>
                    <div className='relative group'>
                      <img
                        src={program.image}
                        alt={program.title}
                        className='rounded-2xl shadow-2xl group-hover:shadow-3xl transition-all duration-500 group-hover:scale-105'
                      />
                      <div className='absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl'></div>
                    </div>
                  </div>
                </div>
              </AnimatedSection>
            ))}
          </div>
        </div>
      </section>

      {/* Skills Training Programs */}
      <section className='py-20 bg-gradient-to-br from-gold-50 to-navy-50 dark:from-gray-800 dark:to-gray-900'>
        <div className='container mx-auto px-6'>
          <AnimatedSection>
            <div className='text-center mb-16'>
              <h2 className='text-3xl md:text-4xl font-bold text-navy-800 dark:text-white mb-6'>
                Skills Training Programs
              </h2>
              <p className='text-lg text-gray-700 dark:text-gray-300 max-w-2xl mx-auto'>
                Practical skills that open doors to economic independence and
                personal growth.
              </p>
            </div>
          </AnimatedSection>

          <div className='grid md:grid-cols-2 lg:grid-cols-3 gap-8'>
            {skillsPrograms.map((skill, index) => (
              <AnimatedSection key={index} delay={index * 200}>
                <div className='bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 group hover:scale-105 border border-gold-200 dark:border-gold-700'>
                  <div className='w-12 h-12 bg-gradient-to-r from-navy-500 to-gold-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300'>
                    <skill.icon className='w-6 h-6 text-white' />
                  </div>

                  <h3 className='text-xl font-bold text-navy-800 dark:text-white mb-3'>
                    {skill.name}
                  </h3>

                  <p className='text-gray-700 dark:text-gray-300 mb-4 leading-relaxed'>
                    {skill.description}
                  </p>

                  <div className='space-y-2 text-sm'>
                    <div className='flex justify-between'>
                      <span className='text-gray-600 dark:text-gray-400'>
                        Duration:
                      </span>
                      <span className='text-navy-800 dark:text-white font-semibold'>
                        {skill.duration}
                      </span>
                    </div>
                    <div className='flex justify-between'>
                      <span className='text-gray-600 dark:text-gray-400'>
                        Level:
                      </span>
                      <span className='text-navy-800 dark:text-white font-semibold'>
                        {skill.level}
                      </span>
                    </div>
                  </div>

                  <button className='w-full mt-6 bg-gradient-to-r from-navy-600 to-gold-600 text-white py-2 rounded-lg font-semibold hover:from-navy-700 hover:to-gold-700 transition-all duration-300'>
                    Enroll Now
                  </button>
                </div>
              </AnimatedSection>
            ))}
          </div>
        </div>
      </section>

      {/* Program Impact */}
      <section className='py-20 bg-white dark:bg-gray-900'>
        <div className='container mx-auto px-6'>
          <div className='grid lg:grid-cols-2 gap-12 items-center'>
            <AnimatedSection>
              <div>
                <h2 className='text-3xl md:text-4xl font-bold text-navy-800 dark:text-white mb-6'>
                  Program Impact
                </h2>
                <p className='text-lg text-gray-700 dark:text-gray-300 mb-8 leading-relaxed'>
                  Our programs have created lasting change in the lives of women
                  and their communities. Through comprehensive training and
                  support, we've helped women build sustainable livelihoods and
                  become leaders in their communities.
                </p>

                <div className='grid grid-cols-2 gap-6'>
                  <div className='text-center'>
                    <div className='text-3xl font-bold text-gold-600 dark:text-gold-400 mb-2'>
                      95%
                    </div>
                    <div className='text-sm text-gray-600 dark:text-gray-400'>
                      Program Completion Rate
                    </div>
                  </div>
                  <div className='text-center'>
                    <div className='text-3xl font-bold text-navy-600 dark:text-navy-400 mb-2'>
                      80%
                    </div>
                    <div className='text-sm text-gray-600 dark:text-gray-400'>
                      Employment Success
                    </div>
                  </div>
                  <div className='text-center'>
                    <div className='text-3xl font-bold text-gold-600 dark:text-gold-400 mb-2'>
                      1000+
                    </div>
                    <div className='text-sm text-gray-600 dark:text-gray-400'>
                      Women Trained
                    </div>
                  </div>
                  <div className='text-center'>
                    <div className='text-3xl font-bold text-navy-600 dark:text-navy-400 mb-2'>
                      25+
                    </div>
                    <div className='text-sm text-gray-600 dark:text-gray-400'>
                      Years of Service
                    </div>
                  </div>
                </div>
              </div>
            </AnimatedSection>

            <AnimatedSection delay={300}>
              <div className='relative'>
                <img
                  src='https://images.unsplash.com/photo-1605810230434-7631ac76ec81?w=600&h=400&fit=crop&crop=center'
                  alt='Program participants'
                  className='rounded-2xl shadow-2xl'
                />
                <div className='absolute -bottom-6 -right-6 bg-gradient-to-r from-navy-500 to-gold-500 text-white p-6 rounded-2xl shadow-xl'>
                  <GraduationCap className='w-8 h-8 mb-2' />
                  <div className='text-sm font-semibold'>
                    Empowering Through Education
                  </div>
                </div>
              </div>
            </AnimatedSection>
          </div>
        </div>
      </section>

      <GallerySection />
      <Footer />
    </div>
  );
};

export default Programs;
