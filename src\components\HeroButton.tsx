
import React from 'react';
import { Heart, Target, Users } from 'lucide-react';
import { useScrollTo } from '@/hooks/useScrollTo';

interface HeroButtonProps {
  text: string;
  target: string;
  variant: 'primary' | 'secondary' | 'outline';
  icon: string;
}

const iconMap = {
  heart: Heart,
  target: Target,
  users: Users,
};

const HeroButton: React.FC<HeroButtonProps> = ({ text, target, variant, icon }) => {
  const scrollTo = useScrollTo();
  const IconComponent = iconMap[icon as keyof typeof iconMap];

  const baseClasses = "group px-10 py-5 rounded-2xl font-semibold text-lg transition-all duration-500 transform hover:scale-105 flex items-center justify-center space-x-3 shadow-xl";
  
  const variantClasses = {
    primary: "bg-gradient-to-r from-amber-500 to-yellow-500 text-white hover:from-amber-400 hover:to-yellow-400 hover:shadow-2xl font-bold",
    secondary: "bg-white/90 backdrop-blur-sm border-2 border-gray-200 text-gray-800 hover:bg-white hover:border-gray-300 hover:shadow-xl",
    outline: "border-2 border-amber-400 text-amber-600 hover:bg-amber-50 hover:border-amber-500"
  };

  return (
    <button
      onClick={() => scrollTo(target)}
      className={`${baseClasses} ${variantClasses[variant]}`}
      aria-label={`Navigate to ${target} section`}
    >
      {IconComponent && <IconComponent className="w-6 h-6 group-hover:animate-pulse" />}
      <span>{text}</span>
    </button>
  );
};

export default React.memo(HeroButton);
