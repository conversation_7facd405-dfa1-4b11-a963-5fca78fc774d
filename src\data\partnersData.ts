import { images } from '@/utils/images';

export interface Partner {
  id: string;
  name: string;
  type: 'international' | 'local' | 'government' | 'ngo' | 'private';
  logo: string;
  website?: string;
  description: string;
  partnership: {
    since: string;
    focus: string[];
    impact: string;
  };
  contact?: {
    email?: string;
    phone?: string;
    address?: string;
  };
  status: 'active' | 'inactive' | 'pending';
}

export interface PartnerCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  partners: Partner[];
}

// Partners Data
export const partners: Partner[] = [
  // International Partners
  {
    id: 'unhcr',
    name: 'UNHCR',
    type: 'international',
    logo: images.partners.international.unhcr,
    website: 'https://www.unhcr.org',
    description: 'The UN Refugee Agency works to protect and assist refugees, displaced communities, and stateless people worldwide.',
    partnership: {
      since: '2020',
      focus: ['Refugee Support', 'Protection Services', 'Community Integration', 'Capacity Building'],
      impact: 'Supporting over 500 refugee women through protection services and integration programs.',
    },
    contact: {
      email: '<EMAIL>',
      address: 'Nairobi, Kenya',
    },
    status: 'active',
  },
  {
    id: 'inkomo<PERSON>',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    type: 'international',
    logo: images.partners.international.inkomoko,
    website: 'https://www.inkomoko.com',
    description: 'Inkomoko supports refugees and host community entrepreneurs to start and grow sustainable businesses.',
    partnership: {
      since: '2021',
      focus: ['Business Training', 'Entrepreneurship', 'Financial Literacy', 'Market Access'],
      impact: 'Trained 200+ women in business skills and supported 50+ successful business launches.',
    },
    contact: {
      email: '<EMAIL>',
      address: 'Nairobi, Kenya',
    },
    status: 'active',
  },
  {
    id: 'refuge-point',
    name: 'RefugePoint',
    type: 'international',
    logo: images.partners.international.refugePoint,
    website: 'https://www.refugepoint.org',
    description: 'RefugePoint identifies and advocates for lasting solutions for the most at-risk refugees.',
    partnership: {
      since: '2019',
      focus: ['Refugee Identification', 'Resettlement Support', 'Protection', 'Advocacy'],
      impact: 'Identified and supported resettlement for 150+ vulnerable refugee women and families.',
    },
    contact: {
      email: '<EMAIL>',
      address: 'Nairobi, Kenya',
    },
    status: 'active',
  },

  // Local Partners
  {
    id: 'local-government',
    name: 'Nairobi County Government',
    type: 'government',
    logo: images.partners.local.pLg,
    description: 'Local government partner supporting community development and women empowerment initiatives.',
    partnership: {
      since: '2018',
      focus: ['Community Development', 'Women Empowerment', 'Local Integration', 'Resource Allocation'],
      impact: 'Provided venue and resources for training programs reaching 300+ women annually.',
    },
    contact: {
      address: 'Nairobi County, Kenya',
    },
    status: 'active',
  },
  {
    id: 'kayole-community',
    name: 'Kayole Community Center',
    type: 'local',
    logo: images.backgrounds.main, // Placeholder - can be updated with actual logo
    description: 'Local community center providing space and resources for WOPEDE programs.',
    partnership: {
      since: '2017',
      focus: ['Venue Provision', 'Community Outreach', 'Local Support', 'Resource Sharing'],
      impact: 'Hosts weekly training sessions and community meetings for 100+ participants.',
    },
    contact: {
      address: 'Kayole, Nairobi, Kenya',
    },
    status: 'active',
  },

  // NGO Partners
  {
    id: 'womens-network',
    name: 'Kenya Women\'s Network',
    type: 'ngo',
    logo: images.backgrounds.main, // Placeholder - can be updated with actual logo
    description: 'Network of women\'s organizations working together for gender equality and empowerment.',
    partnership: {
      since: '2020',
      focus: ['Advocacy', 'Network Building', 'Resource Sharing', 'Capacity Building'],
      impact: 'Connected WOPEDE with 20+ other women\'s organizations for collaborative programs.',
    },
    status: 'active',
  },
  {
    id: 'peace-foundation',
    name: 'East Africa Peace Foundation',
    type: 'ngo',
    logo: images.backgrounds.main, // Placeholder - can be updated with actual logo
    description: 'Foundation focused on peacebuilding and conflict resolution in East Africa.',
    partnership: {
      since: '2019',
      focus: ['Peacebuilding', 'Conflict Resolution', 'Community Healing', 'Training'],
      impact: 'Trained 50+ women as peace ambassadors in their communities.',
    },
    status: 'active',
  },

  // Private Sector Partners
  {
    id: 'local-bank',
    name: 'Community Savings Bank',
    type: 'private',
    logo: images.backgrounds.main, // Placeholder - can be updated with actual logo
    description: 'Local bank providing financial services and literacy training to WOPEDE beneficiaries.',
    partnership: {
      since: '2021',
      focus: ['Financial Services', 'Savings Programs', 'Financial Literacy', 'Microfinance'],
      impact: 'Opened 200+ savings accounts and provided financial literacy training to 300+ women.',
    },
    status: 'active',
  },
  {
    id: 'textile-company',
    name: 'Nairobi Textile Co.',
    type: 'private',
    logo: images.backgrounds.main, // Placeholder - can be updated with actual logo
    description: 'Textile company providing materials and market access for WOPEDE tailoring graduates.',
    partnership: {
      since: '2022',
      focus: ['Material Supply', 'Market Access', 'Skills Training', 'Employment'],
      impact: 'Employed 25+ WOPEDE graduates and provided materials for training programs.',
    },
    status: 'active',
  },
];

// Partner Categories
export const partnerCategories: PartnerCategory[] = [
  {
    id: 'international',
    name: 'International Partners',
    description: 'Global organizations supporting our mission through funding, expertise, and resources.',
    icon: '🌍',
    partners: partners.filter(p => p.type === 'international'),
  },
  {
    id: 'government',
    name: 'Government Partners',
    description: 'Government agencies and departments supporting community development initiatives.',
    icon: '🏛️',
    partners: partners.filter(p => p.type === 'government'),
  },
  {
    id: 'local',
    name: 'Local Community Partners',
    description: 'Local organizations and community groups working together for positive change.',
    icon: '🤝',
    partners: partners.filter(p => p.type === 'local'),
  },
  {
    id: 'ngo',
    name: 'NGO Partners',
    description: 'Non-governmental organizations collaborating on various development programs.',
    icon: '🎯',
    partners: partners.filter(p => p.type === 'ngo'),
  },
  {
    id: 'private',
    name: 'Private Sector Partners',
    description: 'Private companies supporting through employment, training, and market access.',
    icon: '🏢',
    partners: partners.filter(p => p.type === 'private'),
  },
];

// Active Partners
export const activePartners = partners.filter(p => p.status === 'active');

// Featured Partners (major supporters)
export const featuredPartners = partners.filter(p => 
  ['unhcr', 'inkomoko', 'refuge-point', 'local-government'].includes(p.id)
);

// Partnership Statistics
export const partnershipStats = {
  totalPartners: partners.length,
  activePartners: activePartners.length,
  internationalPartners: partners.filter(p => p.type === 'international').length,
  localPartners: partners.filter(p => p.type === 'local').length,
  ngoPartners: partners.filter(p => p.type === 'ngo').length,
  privatePartners: partners.filter(p => p.type === 'private').length,
  governmentPartners: partners.filter(p => p.type === 'government').length,
  partnershipYears: Math.max(...partners.map(p => new Date().getFullYear() - parseInt(p.partnership.since))),
};

// Partnership Focus Areas
export const focusAreas = [
  ...new Set(partners.flatMap(p => p.partnership.focus))
].sort();
