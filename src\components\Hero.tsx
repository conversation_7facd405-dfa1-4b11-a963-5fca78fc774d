import React, { useEffect, useState } from 'react';
import {
  <PERSON>D<PERSON>,
  <PERSON><PERSON><PERSON>,
  Heart,
  Users,
  Award,
  Play,
  BookOpen,
} from 'lucide-react';
import { useScrollTo } from '@/hooks/useScrollTo';
import AnimatedSection from './AnimatedSection';
import CustomButton from './CustomButton';
import { Link } from 'react-router-dom';
import { images } from '@/utils/images';

const Hero: React.FC = () => {
  const scrollTo = useScrollTo();
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const stats = [
    {
      number: '25+',
      title: 'Years of Service',
      description: 'Empowering women since 1999',
      delay: 1800,
    },
    {
      number: '1000+',
      title: 'Lives Transformed',
      description: 'Women empowered with skills',
      delay: 2100,
    },
    {
      number: '6',
      title: 'Training Programs',
      description: 'Comprehensive skill development',
      delay: 2400,
    },
  ];

  return (
    <section className='relative min-h-screen flex items-center overflow-hidden bg-gradient-to-br from-slate-500 via-blue-100/20 to-sky-100/50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 section-padding'>
      {/* Seamless Background Image Integration - Right Side */}
      <div
        className='absolute top-0  right-0 h-full w-1/3'
        style={{
          backgroundImage: `url(${images.hero.background})`,
          backgroundPosition: 'right top',
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
        }}
      >
        {/* Multi-layer seamless blending */}
        {/* <div className='absolute inset-0 bg-gradient-to-l from-transparent from-0% via-slate-50/20 via-30% to-slate-50/60 to-70% dark:from-transparent dark:via-gray-800/20 dark:to-gray-900/80'></div>
        <div className='absolute inset-0 bg-gradient-to-l from-transparent from-0% via-blue-50/10 via-40% to-blue-50/40 to-100%'></div>
        <div className='absolute inset-0 bg-gradient-to-t from-slate-100/30 via-transparent to-transparent'></div> */}
      </div>

      {/* Subtle pattern overlay for texture */}
      <div className='absolute inset-0 opacity-[0.02] bg-gradient-to-br from-navy-900 via-transparent to-navy-900'></div>

      {/* Dynamic Background Elements */}
      <div className='absolute inset-0 pointer-events-none'>
        {/* <div
          className='absolute w-48 h-48 bg-gradient-to-r from-gold-300/20 to-navy-300/20 rounded-full blur-2xl animate-morph'
          style={{
            top: '25%',
            right: '10%',
            transform: `translate(${mousePosition.x * 0.02}px, ${
              mousePosition.y * 0.02
            }px)`,
          }}
        />
        <div
          className='absolute w-40 h-40 bg-gradient-to-r from-navy-400/20 to-gold-400/20 rounded-full blur-2xl animate-morph'
          style={{
            top: '70%',
            left: '25%',
            animationDelay: '4s',
            transform: `translate(${mousePosition.x * -0.01}px, ${
              mousePosition.y * -0.01
            }px)`,
          }}
        />
        <div
          className='absolute w-56 h-56 bg-gradient-to-r from-gold-200/10 to-navy-200/10 rounded-full blur-2xl animate-morph'
          style={{
            top: '45%',
            right: '30%',
            animationDelay: '6s',
            transform: `translate(${mousePosition.x * 0.015}px, ${
              mousePosition.y * 0.015
            }px)`,
          }}
        /> */}
      </div>

      {/* Badge - Positioned to avoid navbar conflict */}
      <div className='absolute top-20 left-1/2 transform -translate-x-1/2 z-20'>
        <AnimatedSection delay={0}>
          <div className='inline-flex items-center space-x-2 bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm px-4 py-2 rounded-full shadow-lg border border-gold-200/50 dark:border-gold-700/50'>
            <Sparkles className='w-3 h-3 text-gold-500 animate-pulse' />
            <span className='text-navy-700 dark:text-gold-400 font-semibold text-xs'>
              Since 1999 • Transforming Lives
            </span>
          </div>
        </AnimatedSection>
      </div>

      {/* Main Content Container */}
      <div
        className='relative z-10 w-full pt-10
      '
      >
        {/* Content Grid */}
        <div className='content-max-width '>
          <div className='grid grid-cols-12 gap-6 min-h-[80vh] items-center'>
            <div className='col-span-12 lg:col-span-7 text-left pl-4 lg:pl-6'>
              <AnimatedSection delay={300}>
                <div className='mb-5'>
                  <h1 className='heading-xl mb-4 leading-[0.9]'>
                    <span className='block mb-3 text-slate-800 dark:text-white animate-hero-fade-in drop-shadow-sm'>
                      Empowering
                    </span>
                    <span className='block mb-3 bg-gradient-to-r from-blue-600 via-sky-500 to-indigo-600 bg-clip-text text-transparent animate-hero-fade-in drop-shadow-lg font-extrabold'>
                      Women
                    </span>
                    <span className='block text-slate-700 dark:text-gray-100 animate-hero-fade-in drop-shadow-sm'>
                      Building Peace
                    </span>
                  </h1>
                  {/* Decorative accent */}
                  <div className='w-24 h-1.5 bg-gradient-to-r from-blue-500 to-sky-400 rounded-full shadow-lg'></div>
                </div>
              </AnimatedSection>

              {/* Enhanced Subtitle */}
              <AnimatedSection delay={600}>
                <div className='mb-7 max-w-2xl'>
                  <p className='text-xl md:text-xl lg:text-2xl text-slate-600 dark:text-gray-300 mb-6 leading-relaxed font-light tracking-wide'>
                    Transforming lives through skills training, counseling and
                    community support since{' '}
                    <span className='font-semibold text-blue-600 dark:text-blue-400'>
                      1999
                    </span>
                  </p>
                  <div className='flex items-center space-x-2 text-slate-500 dark:text-gray-400'>
                    <div className='w-2 h-2 bg-blue-500 rounded-full animate-pulse'></div>
                    <span className='text-sm font-medium tracking-wider uppercase'>
                      25+ Years of Impact
                    </span>
                  </div>
                </div>
              </AnimatedSection>

              {/* Compelling CTA Buttons */}
              <AnimatedSection delay={900}>
                <div className='flex flex-col sm:flex-row gap-6 mb-8'>
                  <CustomButton
                    to='/about'
                    variant='primary'
                    size='lg'
                    icon={BookOpen}
                    iconPosition='left'
                  >
                    Discover Our Story
                  </CustomButton>

                  <CustomButton
                    to='/impact'
                    variant='ghost'
                    size='lg'
                    icon={Award}
                    iconPosition='left'
                  >
                    See Our Impact
                  </CustomButton>
                </div>
              </AnimatedSection>

              {/* Enhanced Stats Grid */}
              <AnimatedSection delay={1200}>
                <div className='grid grid-cols-3 gap-8 mb-8 max-w-3xl'>
                  {stats.map((stat, index) => (
                    <div key={index} className='group text-left'>
                      <div className='bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-1.3 border border-slate-200/50 dark:border-slate-600/50'>
                        <div className='text-2xl sm:text-3xl lg:text-4xl font-black bg-gradient-to-r from-blue-600 via-sky-500 to-indigo-600 bg-clip-text text-transparent mb-3 group-hover:scale-105 transition-transform duration-300'>
                          {stat.number}
                        </div>
                        <div className='text-sm font-bold text-slate-800 dark:text-white mb-2 tracking-wide uppercase'>
                          {stat.title}
                        </div>
                        <div className='text-xs text-slate-600 dark:text-gray-400 leading-relaxed'>
                          {stat.description}
                        </div>
                        <div className='w-full h-0.5 bg-gradient-to-r from-blue-500/50 to-transparent mt-4 group-hover:from-blue-500 transition-all duration-300'></div>
                      </div>
                    </div>
                  ))}
                </div>
              </AnimatedSection>

              {/* Call to Action */}
              <AnimatedSection delay={1500}>
                <div className='flex justify-start'>
                  <Link
                    to='/contact'
                    className='group inline-flex items-center space-x-2 text-navy-800 dark:text-navy-400 font-semibold text-base hover:text-gold-600 dark:hover:text-gold-400 transition-colors duration-300'
                  >
                    <span>Join Our Mission</span>
                    <Users className='w-4 h-4 group-hover:animate-pulse' />
                    <div className='w-0 group-hover:w-6 h-0.5 bg-gold-500 transition-all duration-300'></div>
                  </Link>
                </div>
              </AnimatedSection>
            </div>
          </div>

          {/* Right side - Background image area with floating elements */}
          <div className=' '>
            {/* Floating content aligned with background image */}
            <div className='relative w-full h-full flex flex-col justify-center '>
              {/* Main floating card - positioned in upper area */}
              <AnimatedSection delay={1800}>
                <div className='relative'>
                  {/* <div className='bg-white/95 dark:bg-slate-800/95 backdrop-blur-sm rounded-2xl p-6 shadow-professional border border-slate-200/50 dark:border-slate-600/50 max-w-sm ml-auto transform hover:scale-105 transition-all duration-300'> */}
                  {/* <div className='flex items-center space-x-4'>
                      <div className='w-14 h-14 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center shadow-lg'>
                        <Heart className='w-7 h-7 text-white' />
                      </div>
                      <div>
                        <h4 className='font-semibold text-slate-800 dark:text-white text-base'>
                          Empowering Women
                        </h4>
                        <p className='text-sm text-slate-600 dark:text-gray-400'>
                          Since 1999
                        </p>
                      </div>
                    </div> */}
                  {/* </div> */}

                  {/* Decorative floating elements */}
                  <div className='absolute -top- -right-6 w-16 h-16 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full opacity-20 animate-float-gentle'></div>
                  <div
                    className='absolute -top-6 -right-10 w-20 h-20 bg-gradient-to-br from-sky-400 to-blue-500 rounded-full opacity-15 animate-float-gentle'
                    style={{ animationDelay: '2s' }}
                  ></div>
                </div>
              </AnimatedSection>

              {/* Founder Attribution - positioned closer below */}
              <AnimatedSection delay={2200}>
                <div className='bg-white/95 dark:bg-slate-800/95 backdrop-blur-sm border border-slate-200/50 dark:border-slate-600/50 rounded-xl  shadow-professional max-w-xs ml-auto transform hover:scale-105 transition-all duration-300'>
                  <div className='text-left'>
                    <div className='flex items-center space-x-2 mb-2'>
                      <div className='w-2 h-2 bg-blue-500 rounded-full'></div>
                      <p className='text-sm font-semibold text-slate-700 dark:text-gray-300'>
                        Founded by
                      </p>
                    </div>
                    <p className='text-base font-bold text-blue-700 dark:text-blue-400 mb-1'>
                      Mme Cimpaye Modeste
                    </p>
                    <p className='text-sm text-slate-500 dark:text-gray-500'>
                      25+ years of community impact
                    </p>
                  </div>
                </div>
              </AnimatedSection>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <AnimatedSection delay={2700}>
        <div className='absolute bottom-2 left-1/2 transform -translate-x-1/2'>
          <button
            className='inline-flex flex-col items-center cursor-pointer group'
            onClick={() => scrollTo('programs')}
            aria-label='Scroll to programs section'
          >
            {/* <p className='text-gray-600 dark:text-gray-400 font-medium mb-2 group-hover:text-gold-600 dark:group-hover:text-gold-400 transition-colors duration-500 text-xs'>
              Discover our programs
            </p> */}
            <div className='w-10 h-10 rounded-full border-2 border-gold-500 flex items-center justify-center group-hover:bg-gold-50 dark:group-hover:bg-gold-900/20 transition-all duration-500 group-hover:border-gold-600'>
              <ArrowDown
                className='animate-bounce text-gold-500 group-hover:text-gold-600 dark:group-hover:text-gold-400 transition-colors duration-500'
                size={16}
              />
            </div>
          </button>
        </div>
      </AnimatedSection>
    </section>
  );
};

export default React.memo(Hero);
