import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Heart, Users, Award, Play } from 'lucide-react';
import { useScrollTo } from '@/hooks/useScrollTo';
import AnimatedSection from './AnimatedSection';
import { Link } from 'react-router-dom';

const Hero: React.FC = () => {
  const scrollTo = useScrollTo();
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const stats = [
    {
      number: '25+',
      title: 'Years of Service',
      description: 'Empowering women since 1999',
      delay: 1800,
    },
    {
      number: '1000+',
      title: 'Lives Transformed',
      description: 'Women empowered with skills',
      delay: 2100,
    },
    {
      number: '6',
      title: 'Training Programs',
      description: 'Comprehensive skill development',
      delay: 2400,
    },
  ];

  return (
    <section className='relative min-h-screen flex items-center overflow-hidden bg-gradient-to-br from-navy-50 via-white to-gold-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 py-8'>
      {/* Background Image with Gradient Overlay - Right Side */}
      <div
        className='absolute inset-y-0 right-0 w-1/3'
        style={{
          backgroundImage:
            'url(https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=800&h=1200&fit=crop&crop=center)',
          backgroundPosition: 'center right',
          backgroundSize: 'cover',
          backgroundRepeat: 'no-repeat',
        }}
      >
        {/* Clear to blur gradient overlay - Right to Left with better blending */}
        <div className='absolute inset-0 bg-gradient-to-l from-transparent from-0% via-transparent via-30% to-white/95 to-100% dark:to-gray-900/95'></div>
      </div>

      {/* Dynamic Background Elements */}
      <div className='absolute inset-0 pointer-events-none'>
        <div
          className='absolute w-48 h-48 bg-gradient-to-r from-gold-300/20 to-navy-300/20 rounded-full blur-3xl animate-morph'
          style={{
            top: '25%',
            right: '10%',
            transform: `translate(${mousePosition.x * 0.02}px, ${
              mousePosition.y * 0.02
            }px)`,
          }}
        />
        <div
          className='absolute w-40 h-40 bg-gradient-to-r from-navy-400/20 to-gold-400/20 rounded-full blur-3xl animate-morph'
          style={{
            top: '70%',
            left: '25%',
            animationDelay: '4s',
            transform: `translate(${mousePosition.x * -0.01}px, ${
              mousePosition.y * -0.01
            }px)`,
          }}
        />
        <div
          className='absolute w-56 h-56 bg-gradient-to-r from-gold-200/10 to-navy-200/10 rounded-full blur-3xl animate-morph'
          style={{
            top: '45%',
            right: '30%',
            animationDelay: '6s',
            transform: `translate(${mousePosition.x * 0.015}px, ${
              mousePosition.y * 0.015
            }px)`,
          }}
        />
      </div>

      {/* Badge - Positioned to avoid navbar conflict */}
      <div className='absolute top-28 left-1/2 transform -translate-x-1/2 z-20'>
        <AnimatedSection delay={0}>
          <div className='inline-flex items-center space-x-2 bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm px-4 py-2 rounded-full shadow-lg border border-gold-200/50 dark:border-gold-700/50'>
            <Sparkles className='w-3 h-3 text-gold-500 animate-pulse' />
            <span className='text-navy-700 dark:text-gold-400 font-semibold text-xs'>
              Since 1999 • Transforming Lives
            </span>
          </div>
        </AnimatedSection>
      </div>

      {/* Main Content Container */}
      <div className='relative z-10 w-full pt-16'>
        {/* Content Grid */}
        <div className='container mx-auto px-6'>
          <div className='grid grid-cols-12 gap-8 min-h-[80vh] items-center'>
            {/* Left side - Content area (2/3) */}
            <div className='col-span-12 lg:col-span-8 text-left pl-6 lg:pl-12'>
              {/* Hero Title - Left to Right alignment */}
              <AnimatedSection delay={300}>
                <h1 className='text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight'>
                  <span className='block mb-2 text-navy-800 dark:text-white animate-hero-fade-in'>
                    Empowering
                  </span>
                  <span className='block mb-2 bg-gradient-to-r from-gold-500 via-gold-400 to-navy-600 bg-clip-text text-transparent animate-hero-fade-in'>
                    Women
                  </span>
                  <span className='block text-navy-800 dark:text-white animate-hero-fade-in'>
                    Building Peace
                  </span>
                </h1>
              </AnimatedSection>

              {/* Subtitle */}
              <AnimatedSection delay={600}>
                <div className='mb-8 max-w-2xl'>
                  <p className='text-lg md:text-xl text-gray-700 dark:text-gray-300 mb-4 leading-relaxed font-light'>
                    Transforming lives through skills training, counseling and
                    community support since 1999
                  </p>
                </div>
              </AnimatedSection>

              {/* CTA Buttons - Left aligned */}
              <AnimatedSection delay={900}>
                <div className='flex flex-col sm:flex-row gap-4 mb-12'>
                  <Link
                    to='/about'
                    className='group bg-gradient-to-r from-navy-800 to-navy-600 text-white px-8 py-4 rounded-xl font-semibold text-base hover:from-navy-700 hover:to-navy-500 transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl'
                  >
                    <div className='flex items-center space-x-2'>
                      <Heart className='w-5 h-5 group-hover:animate-pulse' />
                      <span>Our Story</span>
                    </div>
                  </Link>

                  <Link
                    to='/impact'
                    className='group bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm text-navy-800 dark:text-white px-8 py-4 rounded-xl font-semibold text-base hover:bg-white dark:hover:bg-gray-800 transition-all duration-300 transform hover:scale-105 shadow-xl border border-gold-200 dark:border-gold-700'
                  >
                    <div className='flex items-center space-x-2'>
                      <Award className='w-5 h-5 group-hover:animate-pulse' />
                      <span>Our Impact</span>
                    </div>
                  </Link>
                </div>
              </AnimatedSection>

              {/* Stats Grid - Left aligned */}
              <AnimatedSection delay={1200}>
                <div className='grid grid-cols-3 gap-6 mb-8 max-w-2xl'>
                  {stats.map((stat, index) => (
                    <div key={index} className='text-left'>
                      <div className='text-2xl sm:text-3xl font-bold bg-gradient-to-r from-gold-500 to-navy-600 bg-clip-text text-transparent mb-1'>
                        {stat.number}
                      </div>
                      <div className='text-sm font-bold text-navy-800 dark:text-white mb-1'>
                        {stat.title}
                      </div>
                      <div className='text-xs text-gray-600 dark:text-gray-400 leading-relaxed'>
                        {stat.description}
                      </div>
                    </div>
                  ))}
                </div>
              </AnimatedSection>

              {/* Call to Action */}
              <AnimatedSection delay={1500}>
                <div className='flex justify-start'>
                  <Link
                    to='/contact'
                    className='group inline-flex items-center space-x-2 text-navy-800 dark:text-navy-400 font-semibold text-base hover:text-gold-600 dark:hover:text-gold-400 transition-colors duration-300'
                  >
                    <span>Join Our Mission</span>
                    <Users className='w-4 h-4 group-hover:animate-pulse' />
                    <div className='w-0 group-hover:w-6 h-0.5 bg-gold-500 transition-all duration-300'></div>
                  </Link>
                </div>
              </AnimatedSection>
            </div>

            {/* Right side - Background image area with floating elements (1/3) */}
            <div className=''>
              {/* Floating decorative elements */}
              <AnimatedSection delay={1800}>
                <div className='relative'>
                  {/* Main floating card */}

                  {/* Secondary floating elements */}
                  <div className='absolute -bottom-2 -right-4 w-16 h-16 bg-gradient-to-br from-navy-400 to-navy-500 rounded-full opacity-20 animate-float-gentle'></div>
                  <div
                    className='absolute -top-8 -left-2 w-20 h-20 bg-gradient-to-br from-gold-400 to-gold-500 rounded-full opacity-20 animate-float-gentle'
                    style={{ animationDelay: '2s' }}
                  ></div>
                </div>
              </AnimatedSection>
            </div>
          </div>
        </div>
      </div>

      {/* Founder Attribution - Bottom positioned with glassmorphism */}
      <div className='absolute bottom-0  right-8  z-20'>
        <AnimatedSection delay={2400}>
          <div className='container mx-auto px-6'>
            <div className='bg-white/20 dark:bg-gray-900/20 backdrop-blur-md border border-white/30 dark:border-gray-700/30 rounded-2xl p-6 shadow-2xl max-w-md mx-auto'>
              <div className='text-center'>
                <p className='text-sm text-gray-700 dark:text-gray-300 mb-2 font-medium'>
                  Founded by{' '}
                  <span className='font-bold text-white dark:text-gold-400'>
                    Mme Cimpaye Modeste
                  </span>
                </p>
                <p className='text-xs text-gray-600 dark:text-gray-400'>
                  Serving communities across Kenya with dedication and impact
                </p>
                <div className='mt-3 flex justify-center'>
                  <div className='w-16 h-0.5 bg-gradient-to-r from-gold-500 to-navy-600 rounded-full'></div>
                </div>
              </div>
            </div>
          </div>
        </AnimatedSection>
      </div>

      {/* Scroll Indicator */}
      <AnimatedSection delay={2700}>
        <div className='absolute bottom-2 left-1/2 transform -translate-x-1/2'>
          <button
            className='inline-flex flex-col items-center cursor-pointer group'
            onClick={() => scrollTo('programs')}
            aria-label='Scroll to programs section'
          >
            {/* <p className='text-gray-600 dark:text-gray-400 font-medium mb-2 group-hover:text-gold-600 dark:group-hover:text-gold-400 transition-colors duration-500 text-xs'>
              Discover our programs
            </p> */}
            <div className='w-10 h-10 rounded-full border-2 border-gold-500 flex items-center justify-center group-hover:bg-gold-50 dark:group-hover:bg-gold-900/20 transition-all duration-500 group-hover:border-gold-600'>
              <ArrowDown
                className='animate-bounce text-gold-500 group-hover:text-gold-600 dark:group-hover:text-gold-400 transition-colors duration-500'
                size={16}
              />
            </div>
          </button>
        </div>
      </AnimatedSection>
    </section>
  );
};

export default React.memo(Hero);
