import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Heart, Users, Award, Play } from 'lucide-react';
import { useScrollTo } from '@/hooks/useScrollTo';
import AnimatedSection from './AnimatedSection';
import { Link } from 'react-router-dom';

const Hero: React.FC = () => {
  const scrollTo = useScrollTo();
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const stats = [
    {
      number: '25+',
      title: 'Years of Service',
      description: 'Empowering women since 1999',
      delay: 1800,
    },
    {
      number: '1000+',
      title: 'Lives Transformed',
      description: 'Women empowered with skills',
      delay: 2100,
    },
    {
      number: '6',
      title: 'Training Programs',
      description: 'Comprehensive skill development',
      delay: 2400,
    },
  ];

  return (
    <section
      className='relative min-h-screen flex flex-col overflow-hidden'
      style={{
        backgroundImage:
          'url(https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=600&h=750&fit=crop&crop=center)',
        backgroundPosition: 'left 26%',
        backgroundSize: '50% 60%',
        backgroundRepeat: 'no-repeat',
      }}
    >
      {/* Background Overlay */}
      <div className='absolute inset-0 bg-gradient-to-br from-navy-50/90 via-white/85 to-gold-50/90 dark:from-gray-900/90 dark:via-gray-800/85 dark:to-gray-900/90'></div>

      {/* Dynamic Background Elements */}
      <div className='absolute inset-0 pointer-events-none'>
        <div
          className='absolute w-80 h-80 bg-gradient-to-r from-gold-300/20 to-navy-300/20 rounded-full blur-3xl animate-morph'
          style={{
            top: '15%',
            left: '10%',
            transform: `translate(${mousePosition.x * 0.02}px, ${
              mousePosition.y * 0.02
            }px)`,
          }}
        />
        <div
          className='absolute w-64 h-64 bg-gradient-to-r from-navy-400/20 to-gold-400/20 rounded-full blur-3xl animate-morph'
          style={{
            top: '60%',
            right: '10%',
            animationDelay: '4s',
            transform: `translate(${mousePosition.x * -0.01}px, ${
              mousePosition.y * -0.01
            }px)`,
          }}
        />
      </div>

      {/* Main Content */}
      <div className='relative z-10 container mx-auto px-6 pt-20 flex-1 flex flex-col'>
        {/* Badge - Centered at top */}
        <AnimatedSection delay={0}>
          <div className='text-center mb-8'>
            <div className='inline-flex items-center space-x-3 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm px-6 py-3 rounded-full shadow-lg border border-gold-200/50 dark:border-gold-700/50'>
              <Sparkles className='w-4 h-4 text-gold-500 animate-pulse' />
              <span className='text-navy-700 dark:text-gold-400 font-semibold text-sm'>
                Since 1999 • Transforming Lives in Kenya
              </span>
            </div>
          </div>
        </AnimatedSection>

        {/* Content Container - Right aligned */}
        <div className='flex-1 flex items-center justify-end'>
          <div className='w-full lg:w-2/3 xl:w-1/2'>
            {/* Content - Right aligned */}
            <div className='text-right'>
              {/* Hero Title */}
              <div className='mb-8'>
                <AnimatedSection delay={300}>
                  <h1 className='text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold mb-4'>
                    <span className='block mb-2 text-navy-800 dark:text-white animate-hero-fade-in'>
                      Empowering
                    </span>
                  </h1>
                </AnimatedSection>

                <AnimatedSection delay={600}>
                  <h1 className='text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold mb-4'>
                    <span className='block mb-2 bg-gradient-to-r from-gold-500 via-gold-400 to-navy-600 bg-clip-text text-transparent animate-hero-fade-in'>
                      Women
                    </span>
                  </h1>
                </AnimatedSection>

                <AnimatedSection delay={900}>
                  <h1 className='text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold'>
                    <span className='block text-navy-800 dark:text-white animate-hero-fade-in'>
                      Building Peace
                    </span>
                  </h1>
                </AnimatedSection>
              </div>

              {/* Subtitle */}
              <AnimatedSection delay={1200}>
                <div className='mb-8 max-w-2xl ml-auto'>
                  <p className='text-lg md:text-xl text-gray-700 dark:text-gray-300 mb-4 leading-relaxed'>
                    Transforming lives through skills training, counseling and
                    community support
                  </p>
                  <p className='text-base text-gray-600 dark:text-gray-400'>
                    Founded by Mme Cimpaye Modeste • Serving since 1999
                  </p>
                </div>
              </AnimatedSection>

              {/* CTA Buttons */}
              <AnimatedSection delay={1500}>
                <div className='flex flex-col sm:flex-row gap-4 justify-end mb-12'>
                  <Link
                    to='/about'
                    className='group bg-gradient-to-r from-navy-800 to-navy-600 text-white px-8 py-4 rounded-xl font-semibold text-base hover:from-navy-700 hover:to-navy-500 transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl'
                  >
                    <div className='flex items-center justify-center space-x-2'>
                      <Heart className='w-5 h-5 group-hover:animate-pulse' />
                      <span>Our Story</span>
                    </div>
                  </Link>

                  <Link
                    to='/impact'
                    className='group bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm text-navy-800 dark:text-white px-8 py-4 rounded-xl font-semibold text-base hover:bg-white dark:hover:bg-gray-800 transition-all duration-300 transform hover:scale-105 shadow-xl border border-gold-200 dark:border-gold-700'
                  >
                    <div className='flex items-center justify-center space-x-2'>
                      <Award className='w-5 h-5 group-hover:animate-pulse' />
                      <span>Our Impact</span>
                    </div>
                  </Link>

                  <Link
                    to='/contact'
                    className='group border-2 border-navy-800 dark:border-navy-400 text-navy-800 dark:text-navy-400 px-8 py-4 rounded-xl font-semibold text-base hover:bg-navy-50 dark:hover:bg-navy-900/20 transition-all duration-300'
                  >
                    <div className='flex items-center justify-center space-x-2'>
                      <Users className='w-5 h-5 group-hover:animate-pulse' />
                      <span>Join Our Mission</span>
                    </div>
                  </Link>
                </div>
              </AnimatedSection>

              {/* Stats Grid */}
              <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mb-8'>
                {stats.map((stat, index) => (
                  <AnimatedSection key={index} delay={stat.delay}>
                    <div className='bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm p-6 rounded-2xl border border-white/40 dark:border-gray-700/40 hover:bg-white/80 dark:hover:bg-gray-800/80 transition-all duration-300 group hover:scale-105 shadow-lg hover:shadow-xl'>
                      <div className='text-3xl sm:text-4xl font-bold bg-gradient-to-r from-gold-500 to-navy-600 bg-clip-text text-transparent mb-2 group-hover:animate-pulse'>
                        {stat.number}
                      </div>
                      <div className='text-lg font-bold text-navy-800 dark:text-white mb-1'>
                        {stat.title}
                      </div>
                      <div className='text-sm text-gray-600 dark:text-gray-400'>
                        {stat.description}
                      </div>
                    </div>
                  </AnimatedSection>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <AnimatedSection delay={2700}>
          <div className='text-center mt-16'>
            <button
              className='inline-flex flex-col items-center cursor-pointer group'
              onClick={() => scrollTo('programs')}
              aria-label='Scroll to programs section'
            >
              <p className='text-gray-600 dark:text-gray-400 font-medium mb-3 group-hover:text-gold-600 dark:group-hover:text-gold-400 transition-colors duration-500 text-sm'>
                Discover our programs
              </p>
              <div className='w-10 h-10 rounded-full border-2 border-gold-500 flex items-center justify-center group-hover:bg-gold-50 dark:group-hover:bg-gold-900/20 transition-all duration-500'>
                <ArrowDown
                  className='animate-bounce text-gold-500 group-hover:text-gold-600 dark:group-hover:text-gold-400 transition-colors duration-500'
                  size={20}
                />
              </div>
            </button>
          </div>
        </AnimatedSection>
      </div>
    </section>
  );
};

export default React.memo(Hero);
