
import React, { useState, useEffect } from 'react';
import { ShoppingBag, ChevronLeft, ChevronRight, Play, Pause } from 'lucide-react';

const ProductGallery = () => {
  const [selectedProduct, setSelectedProduct] = useState(0);
  const [isAutoSliding, setIsAutoSliding] = useState(true);

  const products = [
    {
      name: 'Traditional Woven Baskets',
      description: 'Beautifully handcrafted storage baskets made using traditional weaving techniques passed down through generations.',
      detailedDescription: 'These stunning baskets are woven by skilled refugee women using natural materials and time-honored techniques. Perfect for home organization or as decorative pieces, each basket supports sustainable livelihoods.',
      image: '/assets/bus1.jpg',
      category: 'Traditional Crafts',
      price: '$25 - $45',
      features: ['Eco-friendly materials', 'Handwoven', 'Durable construction', 'Cultural heritage'],
    },
    {
      name: 'Colorful Textile Art',
      description: 'Vibrant textile pieces showcasing the rich cultural traditions of our artisan community.',
      detailedDescription: 'These beautiful textiles represent hours of meticulous work by talented women artisans. Using traditional patterns and modern color combinations, each piece brings warmth and culture to any space.',
      image: '/assets/bus2.jpg',
      category: 'Handwoven Items',
      price: '$35 - $75',
      features: ['Traditional patterns', 'Vibrant colors', 'Cultural significance', 'Unique designs'],
    },
    {
      name: 'Artisan Home Decor',
      description: 'Elegant decorative pieces that blend traditional craftsmanship with contemporary design.',
      detailedDescription: 'Transform your living space with these carefully crafted decorative items. Each piece reflects the artistic vision of refugee women who have turned their skills into sustainable businesses.',
      image: '/assets/bus3.jpg',
      category: 'Home Decor',
      price: '$20 - $60',
      features: ['Modern aesthetics', 'Traditional techniques', 'Versatile design', 'Quality craftsmanship'],
    },
    {
      name: 'Functional Storage Solutions',
      description: 'Practical yet beautiful storage containers that organize your space while showcasing exceptional craftsmanship.',
      detailedDescription: 'These storage solutions combine functionality with beauty, created by women who understand the importance of organized, beautiful living spaces.',
      image: '/assets/bus4.jpg',
      category: 'Storage Solutions',
      price: '$30 - $55',
      features: ['Multi-functional', 'Space-saving', 'Durable materials', 'Aesthetic appeal'],
    },
  ];

  useEffect(() => {
    if (!isAutoSliding) return;
    const interval = setInterval(() => {
      setSelectedProduct((prev) => (prev + 1) % products.length);
    }, 4000);
    return () => clearInterval(interval);
  }, [isAutoSliding, products.length]);

  const nextProduct = () => setSelectedProduct((prev) => (prev + 1) % products.length);
  const prevProduct = () => setSelectedProduct((prev) => (prev - 1 + products.length) % products.length);
  const toggleAutoSlide = () => setIsAutoSliding(!isAutoSliding);

  return (
    <div className='mb-24'>
      <div className='flex items-center justify-center mb-16'>
        <ShoppingBag className='w-8 h-8 text-amber-600 dark:text-amber-400 mr-4' />
        <h3 className='text-4xl font-bold text-gray-800 dark:text-white'>Our Products</h3>
      </div>

      {/* Main Product Display */}
      <div className='grid lg:grid-cols-3 gap-12 mb-16'>
        {/* Featured Product */}
        <div className='lg:col-span-2'>
          <div className='relative group'>
            <div className='aspect-[4/3] rounded-3xl overflow-hidden shadow-2xl bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700'>
              <img
                src={products[selectedProduct].image}
                alt={products[selectedProduct].name}
                className='w-full h-full object-cover transition-transform duration-700 group-hover:scale-105'
              />
              <div className='absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent'></div>
            </div>

            {/* Navigation Controls */}
            <button
              onClick={prevProduct}
              className='absolute left-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:bg-white dark:hover:bg-gray-800 transition-all duration-300 transform hover:scale-110'
            >
              <ChevronLeft className='w-6 h-6 text-gray-700 dark:text-gray-300' />
            </button>

            <button
              onClick={nextProduct}
              className='absolute right-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:bg-white dark:hover:bg-gray-800 transition-all duration-300 transform hover:scale-110'
            >
              <ChevronRight className='w-6 h-6 text-gray-700 dark:text-gray-300' />
            </button>

            {/* Auto-slide Control */}
            <button
              onClick={toggleAutoSlide}
              className='absolute bottom-4 right-4 w-10 h-10 bg-black/50 backdrop-blur-sm text-white rounded-full flex items-center justify-center hover:bg-black/70 transition-all duration-300'
            >
              {isAutoSliding ? <Pause className='w-4 h-4' /> : <Play className='w-4 h-4' />}
            </button>

            {/* Product Counter */}
            <div className='absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-medium'>
              {selectedProduct + 1} / {products.length}
            </div>
          </div>
        </div>

        {/* Product Details */}
        <div className='space-y-6'>
          <div>
            <span className='text-base text-amber-700 dark:text-amber-300 font-semibold bg-amber-100 dark:bg-amber-900/30 px-4 py-2 rounded-full inline-block mb-4'>
              {products[selectedProduct].category}
            </span>
            <h3 className='text-3xl font-bold text-gray-800 dark:text-white mb-4'>
              {products[selectedProduct].name}
            </h3>
            <p className='text-gray-600 dark:text-gray-300 leading-relaxed text-lg mb-6'>
              {products[selectedProduct].detailedDescription}
            </p>
            <div className='text-2xl font-bold text-amber-600 dark:text-amber-400 mb-6'>
              {products[selectedProduct].price}
            </div>
          </div>

          {/* Features */}
          <div>
            <h4 className='text-lg font-semibold text-gray-800 dark:text-white mb-4'>Key Features</h4>
            <div className='space-y-2'>
              {products[selectedProduct].features.map((feature, index) => (
                <div key={index} className='flex items-center'>
                  <div className='w-2 h-2 bg-amber-600 dark:bg-amber-400 rounded-full mr-3'></div>
                  <span className='text-gray-600 dark:text-gray-300'>{feature}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Contact Button */}
          <a
            href='#contact'
            className='inline-block bg-gradient-to-r from-amber-600 to-yellow-600 text-white px-8 py-4 rounded-2xl font-semibold text-lg hover:from-amber-700 hover:to-yellow-700 transition-all duration-500 transform hover:scale-105 shadow-xl'
          >
            Inquire About This Product
          </a>
        </div>
      </div>

      {/* Product Thumbnails */}
      <div className='grid grid-cols-4 md:grid-cols-8 gap-4 mb-8'>
        {products.map((product, index) => (
          <button
            key={index}
            onClick={() => setSelectedProduct(index)}
            className={`aspect-square rounded-xl overflow-hidden transition-all duration-300 transform hover:scale-105 ${
              selectedProduct === index
                ? 'ring-4 ring-amber-600 shadow-lg'
                : 'hover:shadow-md'
            }`}
          >
            <img
              src={product.image}
              alt={product.name}
              className='w-full h-full object-cover'
            />
          </button>
        ))}
      </div>

      <div className='text-center'>
        <p className='text-xl text-gray-600 dark:text-gray-300 mb-8 font-light'>
          Interested in our products or bulk orders?
        </p>
        <a
          href='#contact'
          className='inline-block bg-gradient-to-r from-amber-600 to-yellow-600 text-white px-12 py-4 rounded-2xl font-semibold text-lg hover:from-amber-700 hover:to-yellow-700 transition-all duration-500 transform hover:scale-105 shadow-xl'
        >
          Contact Us for Orders
        </a>
      </div>
    </div>
  );
};

export { ProductGallery };
