import React, { useState, useMemo } from 'react';
import { Search, Filter, Grid, List, ShoppingCart, Star, Heart, Eye } from 'lucide-react';
import AnimatedSection from '@/components/AnimatedSection';

const Products = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('name');

  const categories = [
    { id: 'all', name: 'All Products', count: 24 },
    { id: 'baskets', name: 'Woven Baskets', count: 8 },
    { id: 'textiles', name: 'Textiles', count: 6 },
    { id: 'home-decor', name: 'Home Decor', count: 5 },
    { id: 'storage', name: 'Storage Solutions', count: 5 }
  ];

  const products = [
    {
      id: 1,
      name: 'Traditional Sisal Basket',
      category: 'baskets',
      price: 35,
      originalPrice: 45,
      rating: 4.8,
      reviews: 24,
      image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop&crop=center',
      colors: ['Natural', 'Brown', 'Black'],
      availability: 'In Stock',
      description: 'Handwoven sisal basket perfect for storage and home decoration.',
      features: ['Eco-friendly', 'Handmade', 'Durable', 'Fair Trade']
    },
    {
      id: 2,
      name: 'Colorful Kikoy Textile',
      category: 'textiles',
      price: 28,
      originalPrice: 35,
      rating: 4.9,
      reviews: 18,
      image: 'https://images.unsplash.com/photo-1582582621959-48d27397dc69?w=400&h=400&fit=crop&crop=center',
      colors: ['Blue', 'Red', 'Green', 'Yellow'],
      availability: 'In Stock',
      description: 'Vibrant kikoy textile showcasing traditional East African patterns.',
      features: ['Cotton blend', 'Machine washable', 'Versatile use', 'Cultural heritage']
    },
    {
      id: 3,
      name: 'Decorative Wall Hanging',
      category: 'home-decor',
      price: 42,
      originalPrice: 50,
      rating: 4.7,
      reviews: 15,
      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=400&fit=crop&crop=center',
      colors: ['Multicolor'],
      availability: 'Limited Stock',
      description: 'Beautiful handcrafted wall hanging that adds warmth to any space.',
      features: ['Unique design', 'Lightweight', 'Easy to hang', 'Artisan made']
    },
    {
      id: 4,
      name: 'Woven Storage Basket Set',
      category: 'storage',
      price: 65,
      originalPrice: 80,
      rating: 4.9,
      reviews: 32,
      image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=400&fit=crop&crop=center',
      colors: ['Natural', 'Brown'],
      availability: 'In Stock',
      description: 'Set of three nesting baskets perfect for organizing your home.',
      features: ['Set of 3', 'Nesting design', 'Strong handles', 'Space-saving']
    },
    {
      id: 5,
      name: 'Handwoven Table Runner',
      category: 'textiles',
      price: 32,
      originalPrice: 40,
      rating: 4.6,
      reviews: 21,
      image: 'https://images.unsplash.com/photo-1582582621959-48d27397dc69?w=400&h=400&fit=crop&crop=center',
      colors: ['Earth tones', 'Bright colors'],
      availability: 'In Stock',
      description: 'Elegant table runner that brings traditional craftsmanship to your dining.',
      features: ['Table protection', 'Easy care', 'Traditional patterns', 'Quality weave']
    },
    {
      id: 6,
      name: 'Artisan Jewelry Box',
      category: 'home-decor',
      price: 48,
      originalPrice: 60,
      rating: 4.8,
      reviews: 12,
      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=400&fit=crop&crop=center',
      colors: ['Natural wood', 'Dark stain'],
      availability: 'In Stock',
      description: 'Beautifully crafted wooden jewelry box with traditional motifs.',
      features: ['Multiple compartments', 'Soft lining', 'Secure closure', 'Handcarved details']
    }
  ];

  const filteredProducts = useMemo(() => {
    let filtered = products.filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           product.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
      return matchesSearch && matchesCategory;
    });

    // Sort products
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return a.price - b.price;
        case 'price-high':
          return b.price - a.price;
        case 'rating':
          return b.rating - a.rating;
        case 'name':
        default:
          return a.name.localeCompare(b.name);
      }
    });

    return filtered;
  }, [searchTerm, selectedCategory, sortBy]);

  const ProductCard = ({ product }: { product: typeof products[0] }) => (
    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group overflow-hidden">
      <div className="relative aspect-square overflow-hidden">
        <img
          src={product.image}
          alt={product.name}
          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
        />
        <div className="absolute top-4 right-4 space-y-2">
          <button className="w-10 h-10 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:scale-110 transition-transform duration-300">
            <Heart className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          </button>
          <button className="w-10 h-10 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg hover:scale-110 transition-transform duration-300">
            <Eye className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          </button>
        </div>
        {product.originalPrice > product.price && (
          <div className="absolute top-4 left-4 bg-red-500 text-white px-2 py-1 rounded-lg text-sm font-semibold">
            Sale
          </div>
        )}
        <div className={`absolute bottom-4 left-4 px-2 py-1 rounded-lg text-xs font-semibold ${
          product.availability === 'In Stock' 
            ? 'bg-green-500 text-white' 
            : 'bg-yellow-500 text-black'
        }`}>
          {product.availability}
        </div>
      </div>
      
      <div className="p-6">
        <h3 className="text-lg font-bold text-navy-800 dark:text-white mb-2 group-hover:text-gold-600 dark:group-hover:text-gold-400 transition-colors duration-300">
          {product.name}
        </h3>
        
        <p className="text-gray-600 dark:text-gray-400 text-sm mb-3 line-clamp-2">
          {product.description}
        </p>
        
        <div className="flex items-center space-x-2 mb-3">
          <div className="flex items-center">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`w-4 h-4 ${
                  i < Math.floor(product.rating)
                    ? 'text-yellow-400 fill-current'
                    : 'text-gray-300 dark:text-gray-600'
                }`}
              />
            ))}
          </div>
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {product.rating} ({product.reviews} reviews)
          </span>
        </div>
        
        <div className="flex items-center space-x-2 mb-4">
          <span className="text-xl font-bold text-navy-800 dark:text-white">
            ${product.price}
          </span>
          {product.originalPrice > product.price && (
            <span className="text-sm text-gray-500 line-through">
              ${product.originalPrice}
            </span>
          )}
        </div>
        
        <div className="flex items-center space-x-2 mb-4">
          <span className="text-sm text-gray-600 dark:text-gray-400">Colors:</span>
          <div className="flex space-x-1">
            {product.colors.slice(0, 3).map((color, index) => (
              <div
                key={index}
                className="w-4 h-4 rounded-full border border-gray-300 dark:border-gray-600"
                style={{ backgroundColor: color.toLowerCase() === 'natural' ? '#D2B48C' : color.toLowerCase() }}
                title={color}
              />
            ))}
            {product.colors.length > 3 && (
              <span className="text-xs text-gray-500">+{product.colors.length - 3}</span>
            )}
          </div>
        </div>
        
        <button className="w-full bg-gradient-to-r from-navy-600 to-gold-600 text-white py-3 rounded-xl font-semibold hover:from-navy-700 hover:to-gold-700 transition-all duration-300 flex items-center justify-center space-x-2 group">
          <ShoppingCart className="w-5 h-5 group-hover:animate-pulse" />
          <span>Add to Cart</span>
        </button>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-navy-50 via-white to-gold-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="container mx-auto px-6">
          <AnimatedSection>
            <div className="text-center mb-16">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-navy-800 dark:text-white mb-6">
                Our <span className="bg-gradient-to-r from-gold-500 to-navy-600 bg-clip-text text-transparent">Products</span>
              </h1>
              <p className="text-xl text-gray-700 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
                Beautiful handcrafted products made by skilled women artisans. Every purchase supports sustainable livelihoods and community development.
              </p>
            </div>
          </AnimatedSection>
        </div>
      </section>

      {/* Filters and Search */}
      <section className="py-8 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
        <div className="container mx-auto px-6">
          <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search products..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-gold-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              />
            </div>

            {/* Category Filter */}
            <div className="flex items-center space-x-4">
              <Filter className="w-5 h-5 text-gray-600 dark:text-gray-400" />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-gold-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              >
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name} ({category.count})
                  </option>
                ))}
              </select>
            </div>

            {/* Sort */}
            <div className="flex items-center space-x-4">
              <span className="text-gray-600 dark:text-gray-400">Sort by:</span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-gold-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              >
                <option value="name">Name</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
                <option value="rating">Rating</option>
              </select>
            </div>

            {/* View Mode */}
            <div className="flex items-center space-x-2 bg-gray-100 dark:bg-gray-800 rounded-xl p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-lg transition-colors duration-200 ${
                  viewMode === 'grid'
                    ? 'bg-white dark:bg-gray-700 text-navy-600 dark:text-gold-400 shadow-sm'
                    : 'text-gray-600 dark:text-gray-400'
                }`}
              >
                <Grid className="w-5 h-5" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-lg transition-colors duration-200 ${
                  viewMode === 'list'
                    ? 'bg-white dark:bg-gray-700 text-navy-600 dark:text-gold-400 shadow-sm'
                    : 'text-gray-600 dark:text-gray-400'
                }`}
              >
                <List className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Products Grid */}
      <section className="py-20 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-6">
          <div className="mb-8">
            <p className="text-gray-600 dark:text-gray-400">
              Showing {filteredProducts.length} of {products.length} products
            </p>
          </div>

          <div className={`grid gap-8 ${
            viewMode === 'grid' 
              ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
              : 'grid-cols-1'
          }`}>
            {filteredProducts.map((product, index) => (
              <AnimatedSection key={product.id} delay={index * 100}>
                <ProductCard product={product} />
              </AnimatedSection>
            ))}
          </div>

          {filteredProducts.length === 0 && (
            <div className="text-center py-16">
              <div className="text-gray-400 dark:text-gray-600 mb-4">
                <Search className="w-16 h-16 mx-auto" />
              </div>
              <h3 className="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">
                No products found
              </h3>
              <p className="text-gray-500 dark:text-gray-500">
                Try adjusting your search or filter criteria
              </p>
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default Products;