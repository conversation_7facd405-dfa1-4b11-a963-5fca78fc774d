import React from 'react';
import { ArrowDown } from 'lucide-react';
import AnimatedSection from './AnimatedSection';
import { images } from '@/utils/images';

interface PageHeroProps {
  title: string;
  subtitle?: string;
  description?: string;
  backgroundImage?: string;
  height?: 'sm' | 'md' | 'lg' | 'xl';
  overlay?: 'light' | 'dark' | 'navy' | 'gold';
  textAlign?: 'left' | 'center' | 'right';
  showScrollIndicator?: boolean;
  scrollToId?: string;
}

const PageHero: React.FC<PageHeroProps> = ({
  title,
  subtitle,
  description,
  backgroundImage,
  height = 'md',
  overlay = 'navy',
  textAlign = 'center',
  showScrollIndicator = true,
  scrollToId = 'content',
}) => {
  const heightClasses = {
    sm: 'min-h-[50vh]',
    md: 'min-h-[60vh]',
    lg: 'min-h-[70vh]',
    xl: 'min-h-[80vh]',
  };

  const overlayClasses = {
    light: 'bg-gradient-to-br from-white/90 via-white/80 to-white/70',
    dark: 'bg-gradient-to-br from-gray-900/90 via-gray-800/80 to-gray-700/70',
    navy: 'bg-gradient-to-br from-navy-900/90 via-navy-800/80 to-navy-700/70',
    gold: 'bg-gradient-to-br from-gold-900/90 via-gold-800/80 to-gold-700/70',
  };

  const textAlignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
  };

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className={`relative ${heightClasses[height]} flex items-center justify-center overflow-hidden`}>
      {/* Background Image */}
      <div
        className='absolute inset-0'
        style={{
          backgroundImage: `url(${backgroundImage || images.backgrounds.main})`,
          backgroundPosition: 'center',
          backgroundSize: 'cover',
          backgroundRepeat: 'no-repeat',
        }}
      >
        {/* Primary Gradient Overlay */}
        <div className={`absolute inset-0 ${overlayClasses[overlay]}`}></div>
        
        {/* Secondary Gradient for depth */}
        <div className='absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent'></div>
        
        {/* Subtle pattern overlay */}
        <div className='absolute inset-0 bg-gradient-to-br from-transparent via-white/5 to-transparent'></div>
      </div>

      {/* Content */}
      <div className='relative z-10 container mx-auto px-6 py-20'>
        <div className={`max-w-4xl mx-auto ${textAlignClasses[textAlign]}`}>
          {/* Badge/Subtitle */}
          {subtitle && (
            <AnimatedSection delay={0}>
              <div className='mb-6'>
                <span className='inline-block bg-white/20 dark:bg-gray-800/20 backdrop-blur-sm px-6 py-2 rounded-full text-white/90 font-semibold text-sm border border-white/20'>
                  {subtitle}
                </span>
              </div>
            </AnimatedSection>
          )}

          {/* Main Title */}
          <AnimatedSection delay={200}>
            <h1 className='text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold text-white mb-8 leading-tight'>
              {title}
            </h1>
          </AnimatedSection>

          {/* Description */}
          {description && (
            <AnimatedSection delay={400}>
              <p className='text-xl md:text-2xl text-white/90 mb-8 leading-relaxed font-light max-w-3xl mx-auto'>
                {description}
              </p>
            </AnimatedSection>
          )}
        </div>
      </div>

      {/* Scroll Indicator */}
      {showScrollIndicator && (
        <div className='absolute bottom-8 left-1/2 transform -translate-x-1/2'>
          <AnimatedSection delay={600}>
            <button
              className='inline-flex flex-col items-center cursor-pointer group'
              onClick={() => scrollToSection(scrollToId)}
              aria-label={`Scroll to ${scrollToId} section`}
            >
              <p className='text-white/80 font-medium mb-2 group-hover:text-white transition-colors duration-300 text-sm'>
                Scroll to explore
              </p>
              <div className='w-10 h-10 rounded-full border-2 border-white/50 flex items-center justify-center group-hover:bg-white/10 group-hover:border-white transition-all duration-300'>
                <ArrowDown
                  className='animate-bounce text-white/80 group-hover:text-white transition-colors duration-300'
                  size={16}
                />
              </div>
            </button>
          </AnimatedSection>
        </div>
      )}
    </section>
  );
};

export default React.memo(PageHero);
